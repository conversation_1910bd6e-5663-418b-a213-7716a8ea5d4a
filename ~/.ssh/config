# Global SSH Configuration for Git Hosting Services

# GitHub
Host github.com
    HostName github.com
    User git
    IdentityFile ~/.ssh/id_ed25519_global
    IdentitiesOnly yes

# GitLab
Host gitlab.com
    HostName gitlab.com
    User git
    IdentityFile ~/.ssh/id_ed25519_global
    IdentitiesOnly yes

# Bitbucket
Host bitbucket.org
    HostName bitbucket.org
    User git
    IdentityFile ~/.ssh/id_ed25519_global
    IdentitiesOnly yes

# Generic Git servers
Host *.git.* git.*
    User git
    IdentityFile ~/.ssh/id_ed25519_global
    IdentitiesOnly yes

# Default settings for all hosts
Host *
    ServerAliveInterval 60
    ServerAliveCountMax 30
    TCPKeepAlive yes

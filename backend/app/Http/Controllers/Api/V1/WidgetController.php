<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\Widget;
use App\Models\WidgetConversation;
use App\Models\WidgetMessage;
use App\Models\WidgetAnalytics;
use App\Models\WidgetTest;
use Illuminate\Http\Request;
use App\Services\WidgetService;

class WidgetController extends Controller
{
    protected $service;

    public function __construct()
    {
        $this->service = new WidgetService();
    }

    public function create(Request $request)
    {
        $widget = $this->service->createWidget($request->all());
        return response()->json($widget);
    }


    public function index()
    {
        $widgets = Widget::all();
        return response()->json($widgets);
    }
}
<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Laravel\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    /**
     * Login user with email and password
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string|min:8',
            'remember_me' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $credentials = $request->only('email', 'password');
        $user = User::where('email', $credentials['email'])
                   ->where('is_active', true)
                   ->first();

        if (!$user || !Hash::check($credentials['password'], $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Update last login timestamp
        $user->update(['last_login_at' => now()]);

        // Create token
        $tokenName = $request->boolean('remember_me') ? 'auth-token-remember' : 'auth-token';
        $expiresAt = $request->boolean('remember_me') ? now()->addDays(30) : now()->addDay();
        
        $token = $user->createToken($tokenName, ['*'], $expiresAt);

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'avatar' => $user->avatar,
                    'preferences' => $user->preferences,
                    'timezone' => $user->timezone
                ],
                'token' => $token->plainTextToken,
                'expires_at' => $expiresAt->toISOString()
            ]
        ]);
    }

    /**
     * Quick admin login for demo/testing purposes
     */
    public function quickAdminLogin(Request $request): JsonResponse
    {
        // Find or create super admin user
        $superAdmin = User::where('email', '<EMAIL>')
                         ->where('role', 'admin')
                         ->first();

        if (!$superAdmin) {
            $superAdmin = User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('SuperAdmin123!'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
                'timezone' => 'UTC',
                'preferences' => [
                    'theme' => 'light',
                    'notifications' => true,
                    'language' => 'en'
                ]
            ]);
        }

        // Update last login timestamp
        $superAdmin->update(['last_login_at' => now()]);

        // Create token with extended expiration for demo
        $token = $superAdmin->createToken('quick-admin-token', ['*'], now()->addDays(7));

        return response()->json([
            'success' => true,
            'message' => 'Quick admin login successful',
            'data' => [
                'user' => [
                    'id' => $superAdmin->id,
                    'name' => $superAdmin->name,
                    'email' => $superAdmin->email,
                    'role' => $superAdmin->role,
                    'avatar' => $superAdmin->avatar,
                    'preferences' => $superAdmin->preferences,
                    'timezone' => $superAdmin->timezone
                ],
                'token' => $token->plainTextToken,
                'expires_at' => now()->addDays(7)->toISOString()
            ]
        ]);
    }

    /**
     * Register new user
     */
    public function register(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'timezone' => 'string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => 'user',
            'is_active' => true,
            'timezone' => $request->timezone ?? 'UTC',
            'preferences' => [
                'theme' => 'light',
                'notifications' => true,
                'language' => 'en'
            ]
        ]);

        // Create token
        $token = $user->createToken('auth-token', ['*'], now()->addDay());

        return response()->json([
            'success' => true,
            'message' => 'Registration successful',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'avatar' => $user->avatar,
                    'preferences' => $user->preferences,
                    'timezone' => $user->timezone
                ],
                'token' => $token->plainTextToken,
                'expires_at' => now()->addDay()->toISOString()
            ]
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if ($user) {
            // Revoke current token
            $request->user()->currentAccessToken()->delete();
        }

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Get current authenticated user
     */
    public function me(Request $request): JsonResponse
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'avatar' => $user->avatar,
                    'preferences' => $user->preferences,
                    'timezone' => $user->timezone,
                    'last_login_at' => $user->last_login_at?->toISOString()
                ]
            ]
        ]);
    }

    /**
     * Refresh authentication token
     */
    public function refresh(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        // Revoke current token
        $request->user()->currentAccessToken()->delete();

        // Create new token
        $token = $user->createToken('auth-token-refresh', ['*'], now()->addDay());

        return response()->json([
            'success' => true,
            'message' => 'Token refreshed successfully',
            'data' => [
                'token' => $token->plainTextToken,
                'expires_at' => now()->addDay()->toISOString()
            ]
        ]);
    }

    /**
     * Revoke all user tokens
     */
    public function revokeAll(Request $request): JsonResponse
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        // Revoke all tokens for the user
        $user->tokens()->delete();

        return response()->json([
            'success' => true,
            'message' => 'All tokens revoked successfully'
        ]);
    }
}
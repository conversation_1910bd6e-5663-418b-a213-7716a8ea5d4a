<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\UpdateAiProviderRequest;
use App\Services\AiProviderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AiProviderController extends Controller
{
    protected AiProviderService $aiProviderService;

    public function __construct(AiProviderService $aiProviderService)
    {
        $this->aiProviderService = $aiProviderService;
    }

    /**
     * Display a listing of AI providers
     */
    public function index(): JsonResponse
    {
        try {
            $providers = $this->aiProviderService->getProvidersForUser(Auth::id());

            return response()->json([
                'success' => true,
                'data' => $providers
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch AI providers',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified AI provider
     */
    public function show(string $id): JsonResponse
    {
        try {
            $provider = $this->aiProviderService->getProvider($id, Auth::id());

            return response()->json([
                'success' => true,
                'data' => $provider
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'AI provider not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified AI provider configuration
     */
    public function update(UpdateAiProviderRequest $request, string $id): JsonResponse
    {
        try {
            $provider = $this->aiProviderService->updateProviderConfig(
                $id,
                Auth::id(),
                $request->validated()
            );

            return response()->json([
                'success' => true,
                'data' => $provider,
                'message' => 'AI provider updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update AI provider',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test AI provider connection
     */
    public function test(string $id): JsonResponse
    {
        try {
            $result = $this->aiProviderService->testProvider($id, Auth::id());

            return response()->json([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Provider test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Fetch available models from AI provider
     */
    public function fetchModels(string $id): JsonResponse
    {
        try {
            $models = $this->aiProviderService->fetchModelsFromProvider($id, Auth::id());

            return response()->json([
                'success' => true,
                'data' => $models,
                'message' => 'Models fetched successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch models',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available AI models
     */
    public function models(Request $request): JsonResponse
    {
        try {
            $models = $this->aiProviderService->getAvailableModels(
                Auth::id(),
                $request->get('provider_id')
            );

            return response()->json([
                'success' => true,
                'data' => $models
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch AI models',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

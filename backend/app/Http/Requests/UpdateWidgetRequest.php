<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateWidgetRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // Basic Settings
            'name' => 'sometimes|string|max:100',
            'description' => 'nullable|string|max:500',
            'ai_provider' => 'sometimes|string|exists:ai_providers,slug',
            'ai_model' => 'sometimes|string',
            'system_prompt' => 'nullable|string|max:2000',
            'temperature' => 'nullable|numeric|min:0|max:2',
            'max_tokens' => 'nullable|integer|min:1|max:4000',
            'response_timeout' => 'nullable|integer|min:1000|max:30000',
            'is_active' => 'nullable|boolean',
            
            // Styling
            'primary_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'secondary_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'background_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'text_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'accent_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'error_color' => 'nullable|string|regex:/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/',
            'font_family' => 'nullable|string|in:Inter,Roboto,Open Sans,Lato,Poppins,Montserrat,Source Sans Pro,Nunito,Raleway,Ubuntu',
            'font_size' => 'nullable|integer|min:12|max:20',
            'font_weight' => 'nullable|string|in:normal,medium,semibold,bold',
            'border_radius' => 'nullable|integer|min:0|max:50',
            'shadow_intensity' => 'nullable|string|in:none,sm,md,lg,xl',
            'animation' => 'nullable|string|in:none,fade,slide,bounce,pulse,shake',
            'custom_css' => 'nullable|string|max:5000',
            
            // Behavior
            'auto_open' => 'nullable|boolean',
            'auto_open_delay' => 'nullable|integer|min:0|max:60000',
            'auto_open_trigger' => 'nullable|string|in:time,scroll,exit-intent,page-visit',
            'scroll_trigger_percent' => 'nullable|integer|min:0|max:100',
            'welcome_message' => 'nullable|string|max:500',
            'placeholder_text' => 'nullable|string|max:100',
            'show_typing_indicator' => 'nullable|boolean',
            'typing_indicator_delay' => 'nullable|integer|min:500|max:5000',
            'enable_sound_notifications' => 'nullable|boolean',
            'sound_volume' => 'nullable|numeric|min:0|max:1',
            'show_branding' => 'nullable|boolean',
            'enable_file_upload' => 'nullable|boolean',
            'max_file_size' => 'nullable|integer|min:1|max:10',
            'allowed_file_types' => 'nullable|array',
            'enable_emojis' => 'nullable|boolean',
            'enable_markdown' => 'nullable|boolean',
            'conversation_starters' => 'nullable|array|max:5',
            
            // Positioning
            'position' => 'nullable|string|in:bottom-right,bottom-left,top-right,top-left',
            'offset_x' => 'nullable|integer|min:0|max:200',
            'offset_y' => 'nullable|integer|min:0|max:200',
            'width' => 'nullable|integer|min:300|max:600',
            'height' => 'nullable|integer|min:400|max:800',
            'min_width' => 'nullable|integer|min:250|max:400',
            'min_height' => 'nullable|integer|min:300|max:500',
            'max_width' => 'nullable|integer|min:400|max:1000',
            'max_height' => 'nullable|integer|min:500|max:1200',
            'z_index' => 'nullable|integer|min:1000|max:9999',
            'icon_style' => 'nullable|string|in:chat,message,question,support,help',
            'icon_size' => 'nullable|integer|min:40|max:80',
            'mobile_breakpoint' => 'nullable|integer|min:320|max:768',
            'tablet_breakpoint' => 'nullable|integer|min:768|max:1024',
            'responsive_scaling' => 'nullable|boolean',
            
            // Advanced
            'allowed_domains' => 'nullable|array',
            'blocked_domains' => 'nullable|array',
            'rate_limit_per_minute' => 'nullable|integer|min:1|max:100',
            'rate_limit_per_hour' => 'nullable|integer|min:10|max:1000',
            'enable_analytics' => 'nullable|boolean',
            'analytics_provider' => 'nullable|string|in:google,mixpanel,amplitude,custom',
            'custom_analytics_code' => 'nullable|string|max:2000',
            'enable_a11y' => 'nullable|boolean',
            'keyboard_navigation' => 'nullable|boolean',
            'screen_reader_support' => 'nullable|boolean',
            'high_contrast_mode' => 'nullable|boolean',
            'enable_gdpr' => 'nullable|boolean',
            'gdpr_message' => 'nullable|string|max:500',
            'cookie_consent' => 'nullable|boolean',
            'data_retention_days' => 'nullable|integer|min:1|max:365',
            'enable_encryption' => 'nullable|boolean',
            'webhook_url' => 'nullable|url',
            'webhook_events' => 'nullable|array',
            'custom_headers' => 'nullable|array',
            'enable_cors' => 'nullable|boolean',
            'cors_origins' => 'nullable|array',
            
            // Device Preview
            'current_device' => 'nullable|string|in:desktop,tablet,mobile',
            'custom_width' => 'nullable|integer|min:320|max:2560',
            'custom_height' => 'nullable|integer|min:568|max:1440',
            'orientation' => 'nullable|string|in:portrait,landscape',
            'show_device_frame' => 'nullable|boolean',
            'device_scale' => 'nullable|numeric|min:0.25|max:2',
            
            // Testing
            'enable_testing' => 'nullable|boolean',
            'test_scenarios' => 'nullable|array',
            'performance_monitoring' => 'nullable|boolean',
            'error_tracking' => 'nullable|boolean',
            'load_testing' => 'nullable|boolean',
            'max_concurrent_users' => 'nullable|integer|min:1|max:1000',
            
            // Meta
            'tags' => 'nullable|array',
            'category' => 'nullable|string',
            'priority' => 'nullable|string|in:low,medium,high,critical',
        ];
    }

    public function messages(): array
    {
        return [
            'name.max' => 'Widget name cannot exceed 100 characters.',
            'ai_provider.exists' => 'Selected AI provider is invalid.',
            'primary_color.regex' => 'Primary color must be a valid hex color.',
            'secondary_color.regex' => 'Secondary color must be a valid hex color.',
            'background_color.regex' => 'Background color must be a valid hex color.',
            'text_color.regex' => 'Text color must be a valid hex color.',
            'accent_color.regex' => 'Accent color must be a valid hex color.',
            'error_color.regex' => 'Error color must be a valid hex color.',
        ];
    }
}

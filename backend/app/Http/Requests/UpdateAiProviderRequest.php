<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAiProviderRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'api_key' => 'nullable|string|min:10|max:255',
            'settings' => 'nullable|array',
            'settings.temperature' => 'nullable|numeric|min:0|max:2',
            'settings.max_tokens' => 'nullable|integer|min:1|max:8000',
            'settings.top_p' => 'nullable|numeric|min:0|max:1',
            'settings.frequency_penalty' => 'nullable|numeric|min:-2|max:2',
            'settings.presence_penalty' => 'nullable|numeric|min:-2|max:2',
            'settings.timeout' => 'nullable|integer|min:5|max:120',
            'settings.retry_attempts' => 'nullable|integer|min:0|max:5',
        ];
    }

    public function messages(): array
    {
        return [
            'api_key.min' => 'API key must be at least 10 characters long.',
            'api_key.max' => 'API key cannot exceed 255 characters.',
            'settings.temperature.min' => 'Temperature must be between 0 and 2.',
            'settings.temperature.max' => 'Temperature must be between 0 and 2.',
            'settings.max_tokens.min' => 'Max tokens must be at least 1.',
            'settings.max_tokens.max' => 'Max tokens cannot exceed 8000.',
            'settings.top_p.min' => 'Top P must be between 0 and 1.',
            'settings.top_p.max' => 'Top P must be between 0 and 1.',
            'settings.frequency_penalty.min' => 'Frequency penalty must be between -2 and 2.',
            'settings.frequency_penalty.max' => 'Frequency penalty must be between -2 and 2.',
            'settings.presence_penalty.min' => 'Presence penalty must be between -2 and 2.',
            'settings.presence_penalty.max' => 'Presence penalty must be between -2 and 2.',
            'settings.timeout.min' => 'Timeout must be at least 5 seconds.',
            'settings.timeout.max' => 'Timeout cannot exceed 120 seconds.',
            'settings.retry_attempts.min' => 'Retry attempts cannot be negative.',
            'settings.retry_attempts.max' => 'Retry attempts cannot exceed 5.',
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\WidgetConversation;
use App\Models\Widget;

class WidgetMessage extends Model
{
    use HasFactory;

    protected $table = 'widget_messages';

    protected $fillable = [
        'conversation_id',
        'widget_id',
        'role',
        'content',
        'metadata',
        'ai_model_used',
        'tokens_used',
        'response_time',
        'cost',
        'is_error',
        'error_message',
        'attachments',
        'sent_at',
            
    ];

    public function conversation(): BelongsTo
    {
        return $this->belongsTo(WidgetConversation::class);
    }

    public function widget(): BelongsTo
    {
        return $this->belongsTo(Widget::class);
    }
}
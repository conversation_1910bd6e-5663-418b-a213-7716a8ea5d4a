<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogsActivity;
use Spatie\Activitylog\Traits\LogsActivity as LogsActivityTrait;

class AiModel extends Model
{
    use HasFactory, HasUuids, LogsActivityTrait;

    protected $fillable = [
        'ai_provider_id',
        'name',
        'model_id',
        'description',
        'max_tokens',
        'context_length',
        'supports_streaming',
        'supports_functions',
        'supports_vision',
        'supports_json_mode',
        'input_cost_per_1k_tokens',
        'output_cost_per_1k_tokens',
        'is_available',
        'is_deprecated',
        'capabilities',
        'parameters',
        'sort_order',
        'deprecated_at',
    ];

    protected $casts = [
        'supports_streaming' => 'boolean',
        'supports_functions' => 'boolean',
        'supports_vision' => 'boolean',
        'supports_json_mode' => 'boolean',
        'input_cost_per_1k_tokens' => 'decimal:6',
        'output_cost_per_1k_tokens' => 'decimal:6',
        'is_available' => 'boolean',
        'is_deprecated' => 'boolean',
        'capabilities' => 'array',
        'parameters' => 'array',
        'deprecated_at' => 'datetime',
    ];

    protected static $logAttributes = ['name', 'model_id', 'is_available', 'is_deprecated'];
    protected static $logOnlyDirty = true;

    public function aiProvider(): BelongsTo
    {
        return $this->belongsTo(AiProvider::class);
    }

    public function widgets(): HasMany
    {
        return $this->hasMany(Widget::class, 'ai_model', 'model_id');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(WidgetMessage::class, 'ai_model_used', 'model_id');
    }

    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)
                    ->where('is_deprecated', false);
    }

    public function scopeForProvider($query, $providerId)
    {
        return $query->where('ai_provider_id', $providerId);
    }

    public function getFullModelName(): string
    {
        return $this->aiProvider->name . ' - ' . $this->name;
    }

    public function getCostPerMessage(int $inputTokens, int $outputTokens): float
    {
        $inputCost = $this->input_cost_per_1k_tokens ? 
            ($inputTokens / 1000) * $this->input_cost_per_1k_tokens : 0;
        
        $outputCost = $this->output_cost_per_1k_tokens ? 
            ($outputTokens / 1000) * $this->output_cost_per_1k_tokens : 0;
        
        return $inputCost + $outputCost;
    }

    public function isCompatibleWith(array $requirements): bool
    {
        if (isset($requirements['streaming']) && $requirements['streaming'] && !$this->supports_streaming) {
            return false;
        }
        
        if (isset($requirements['functions']) && $requirements['functions'] && !$this->supports_functions) {
            return false;
        }
        
        if (isset($requirements['vision']) && $requirements['vision'] && !$this->supports_vision) {
            return false;
        }
        
        if (isset($requirements['max_tokens']) && $requirements['max_tokens'] > $this->max_tokens) {
            return false;
        }
        
        return true;
    }
}

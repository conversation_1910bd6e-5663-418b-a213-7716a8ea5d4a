<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Widget;
use App\Models\WidgetMessage;

class WidgetConversation extends Model
{
    use HasFactory ; 

    protected $table = 'widget_conversations';

    protected $fillable = [
        'widget_id',
        'session_id',
        'visitor_id',
        'ip_address',
        'user_agent',
        'referrer',
        'country',
        'city',
        'status',
        'started_at',
        'ended_at',
        'message_count',
        'avg_response_time',
        'satisfaction_rating',
        'feedback',
        'metadata', 
        
    ];

    public function widget(): BelongsTo
    {
        return $this->belongsTo(Widget::class);
    }

    public function messages(): HasMany
    {
        return $this->hasMany(WidgetMessage::class);
    }
}   
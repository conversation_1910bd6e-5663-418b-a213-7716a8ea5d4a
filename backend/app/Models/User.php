<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Activitylog\LogsActivity;
use Spatie\Activitylog\Traits\LogsActivity as LogsActivityTrait;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasUuids, LogsActivityTrait;

    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'role',
        'is_active',
        'preferences',
        'timezone',
        'last_login_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'password' => 'hashed',
        'preferences' => 'array',
        'is_active' => 'boolean',
    ];

    protected static $logAttributes = ['name', 'email', 'role', 'is_active'];
    protected static $logOnlyDirty = true;

    public function widgets(): HasMany
    {
        return $this->hasMany(Widget::class);
    }

    public function aiProviderConfigs(): HasMany
    {
        return $this->hasMany(UserAiProviderConfig::class);
    }

    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function getConfiguredAiProviders()
    {
        return $this->aiProviderConfigs()
            ->with('aiProvider')
            ->where('is_configured', true)
            ->where('status', 'active')
            ->get()
            ->pluck('aiProvider');
    }

    public function hasConfiguredProvider(string $providerSlug): bool
    {
        return $this->aiProviderConfigs()
            ->whereHas('aiProvider', fn($q) => $q->where('slug', $providerSlug))
            ->where('is_configured', true)
            ->where('status', 'active')
            ->exists();
    }

    public function getProviderConfig(string $providerSlug): ?UserAiProviderConfig
    {
        return $this->aiProviderConfigs()
            ->whereHas('aiProvider', fn($q) => $q->where('slug', $providerSlug))
            ->first();
    }
}

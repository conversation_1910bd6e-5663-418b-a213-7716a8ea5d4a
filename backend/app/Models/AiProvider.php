<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogsActivity;
use Spatie\Activitylog\Traits\LogsActivity as LogsActivityTrait;

class Ai<PERSON>rovider extends Model
{
    use HasFactory, HasUuids, LogsActivityTrait;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'base_url',
        'headers',
        'default_parameters',
        'api_key_required',
        'api_key_header',
        'api_key_prefix',
        'status',
        'supports_streaming',
        'supports_functions',
        'supports_vision',
        'rate_limit_per_minute',
        'rate_limit_per_hour',
        'cost_per_1k_tokens',
        'supported_formats',
        'configuration_schema',
        'last_tested_at',
        'test_results',
    ];

    protected $casts = [
        'headers' => 'array',
        'default_parameters' => 'array',
        'api_key_required' => 'boolean',
        'supports_streaming' => 'boolean',
        'supports_functions' => 'boolean',
        'supports_vision' => 'boolean',
        'cost_per_1k_tokens' => 'decimal:6',
        'supported_formats' => 'array',
        'configuration_schema' => 'array',
        'last_tested_at' => 'datetime',
        'test_results' => 'array',
    ];

    protected static $logAttributes = ['name', 'slug', 'status'];
    protected static $logOnlyDirty = true;

    public function models(): HasMany
    {
        return $this->hasMany(AiModel::class);
    }

    public function userConfigs(): HasMany
    {
        return $this->hasMany(UserAiProviderConfig::class);
    }

    public function widgets(): HasMany
    {
        return $this->hasMany(Widget::class, 'ai_provider', 'slug');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeConfigured($query, User $user)
    {
        return $query->whereHas('userConfigs', function ($q) use ($user) {
            $q->where('user_id', $user->id)
              ->where('is_configured', true)
              ->where('status', 'active');
        });
    }

    public function getAvailableModels()
    {
        return $this->models()
            ->where('is_available', true)
            ->where('is_deprecated', false)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function isConfiguredForUser(User $user): bool
    {
        return $this->userConfigs()
            ->where('user_id', $user->id)
            ->where('is_configured', true)
            ->where('status', 'active')
            ->exists();
    }

    public function getUserConfig(User $user): ?UserAiProviderConfig
    {
        return $this->userConfigs()
            ->where('user_id', $user->id)
            ->first();
    }

    public function getAuthHeaders(string $apiKey): array
    {
        $headers = $this->headers ?? [];
        
        if ($this->api_key_required && $apiKey) {
            $authValue = $this->api_key_prefix ? 
                $this->api_key_prefix . ' ' . $apiKey : 
                $apiKey;
            
            $headers[$this->api_key_header] = $authValue;
        }
        
        return $headers;
    }
}

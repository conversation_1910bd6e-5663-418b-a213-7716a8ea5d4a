<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogsActivity;
use Spatie\Activitylog\Traits\LogsActivity as LogsActivityTrait;

class UserAiProviderConfig extends Model
{
    use HasFactory, HasUuids, LogsActivityTrait;

    protected $fillable = [
        'user_id',
        'ai_provider_id',
        'api_key',
        'configuration',
        'is_configured',
        'status',
        'last_tested_at',
        'test_results',
        'error_message',
    ];

    protected $hidden = [
        'api_key',
    ];

    protected $casts = [
        'configuration' => 'array',
        'is_configured' => 'boolean',
        'last_tested_at' => 'datetime',
        'test_results' => 'array',
    ];

    protected static $logAttributes = ['is_configured', 'status'];
    protected static $logOnlyDirty = true;

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function aiProvider(): BelongsTo
    {
        return $this->belongsTo(AiProvider::class);
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('is_configured', true);
    }

    public function scopeConfigured($query)
    {
        return $query->where('is_configured', true);
    }

    public function getDecryptedApiKey(): ?string
    {
        return $this->api_key ? decrypt($this->api_key) : null;
    }

    public function setApiKeyAttribute($value): void
    {
        $this->attributes['api_key'] = $value ? encrypt($value) : null;
    }

    public function markAsConfigured(): void
    {
        $this->update([
            'is_configured' => true,
            'status' => 'active',
            'error_message' => null,
        ]);
    }

    public function markAsError(string $errorMessage): void
    {
        $this->update([
            'status' => 'error',
            'error_message' => $errorMessage,
        ]);
    }

    public function updateTestResults(array $results): void
    {
        $this->update([
            'last_tested_at' => now(),
            'test_results' => $results,
            'status' => $results['success'] ? 'active' : 'error',
            'error_message' => $results['success'] ? null : ($results['error'] ?? 'Test failed'),
        ]);
    }

    public function isHealthy(): bool
    {
        return $this->status === 'active' && 
               $this->is_configured && 
               $this->api_key !== null;
    }

    public function needsRetesting(): bool
    {
        if (!$this->last_tested_at) {
            return true;
        }
        
        // Retest if last test was more than 24 hours ago
        return $this->last_tested_at->diffInHours(now()) > 24;
    }
}

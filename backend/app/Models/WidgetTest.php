<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Widget;

class WidgetTest extends Model
{
    use HasFactory;

    protected $table = 'widget_tests';

    protected $fillable = [
        'widget_id',
        'name',
        'description',
        'test_messages',    
        'expected_responses',
        'status',
        'results',
        'passed',
        'score',
        'details',
        'last_run_at',
        'execution_time',
    ];  

    public function widget(): BelongsTo
    {
        return $this->belongsTo(Widget::class);
    }
}
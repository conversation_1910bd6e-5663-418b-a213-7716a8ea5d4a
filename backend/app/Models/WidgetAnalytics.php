<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Widget;

class WidgetAnalytics extends Model
{
    use HasFactory;

    protected $table = 'widget_analytics';

    protected $fillable = [
        'widget_id',
        'date',
        'total_conversations',
        'total_messages',   
        'unique_visitors',
        'avg_response_time',
        'avg_conversation_duration',
        'user_satisfaction',
        'total_tokens_used',
        'total_cost',
        'popular_queries',
        'error_stats',
        'device_stats',
        'location_stats',
        'created_at',
        'updated_at',
    ];

    public function widget(): BelongsTo
    {
        return $this->belongsTo(Widget::class);
    }
}
<?php

namespace App\Services;

use App\Models\Widget;
use App\Models\WidgetAnalytics;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class WidgetService
{
    /**
     * Get widgets for a user with pagination and filtering
     */
    public function getWidgets(string $userId, array $filters = []): LengthAwarePaginator
    {
        $query = Widget::forUser($userId)
            ->with(['aiProvider', 'aiModel'])
            ->orderBy('updated_at', 'desc');

        // Apply search filter
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Apply status filter
        if (!empty($filters['status'])) {
            switch ($filters['status']) {
                case 'published':
                    $query->published();
                    break;
                case 'draft':
                    $query->draft();
                    break;
                case 'archived':
                    $query->where('is_active', false);
                    break;
            }
        }

        // Apply sorting
        if (!empty($filters['sort_by'])) {
            $sortOrder = $filters['sort_order'] ?? 'desc';
            $query->orderBy($filters['sort_by'], $sortOrder);
        }

        $perPage = $filters['per_page'] ?? 15;
        return $query->paginate($perPage);
    }

    /**
     * Get a specific widget
     */
    public function getWidget(string $widgetId, string $userId): Widget
    {
        return Widget::forUser($userId)
            ->with(['aiProvider', 'aiModel', 'conversations', 'analytics'])
            ->findOrFail($widgetId);
    }

    /**
     * Create a new widget
     */
    public function createWidget(string $userId, array $data): Widget
    {
        return DB::transaction(function () use ($userId, $data) {
            $widget = Widget::create(array_merge($data, [
                'user_id' => $userId,
                'version' => '1.0.0',
                'is_draft' => true,
                'is_published' => false,
            ]));

            // Generate initial embed code hash
            $widget->generateEmbedCodeHash();

            return $widget->load(['aiProvider', 'aiModel']);
        });
    }

    /**
     * Update a widget
     */
    public function updateWidget(string $widgetId, string $userId, array $data): Widget
    {
        return DB::transaction(function () use ($widgetId, $userId, $data) {
            $widget = Widget::forUser($userId)->findOrFail($widgetId);
            
            $widget->update($data);
            
            // Regenerate embed code hash if published
            if ($widget->is_published) {
                $widget->generateEmbedCodeHash();
            }

            return $widget->load(['aiProvider', 'aiModel']);
        });
    }

    /**
     * Delete a widget
     */
    public function deleteWidget(string $widgetId, string $userId): bool
    {
        return DB::transaction(function () use ($widgetId, $userId) {
            $widget = Widget::forUser($userId)->findOrFail($widgetId);
            return $widget->delete();
        });
    }

    /**
     * Publish a widget
     */
    public function publishWidget(string $widgetId, string $userId): Widget
    {
        return DB::transaction(function () use ($widgetId, $userId) {
            $widget = Widget::forUser($userId)->findOrFail($widgetId);
            
            if (!$widget->canBePublished()) {
                throw new \Exception('Widget cannot be published. Please ensure all required fields are configured.');
            }
            
            $widget->publish();
            $widget->generateEmbedCodeHash();
            
            return $widget->load(['aiProvider', 'aiModel']);
        });
    }

    /**
     * Unpublish a widget
     */
    public function unpublishWidget(string $widgetId, string $userId): Widget
    {
        return DB::transaction(function () use ($widgetId, $userId) {
            $widget = Widget::forUser($userId)->findOrFail($widgetId);
            $widget->unpublish();
            
            return $widget->load(['aiProvider', 'aiModel']);
        });
    }

    /**
     * Duplicate a widget
     */
    public function duplicateWidget(string $widgetId, string $userId): Widget
    {
        return DB::transaction(function () use ($widgetId, $userId) {
            $originalWidget = Widget::forUser($userId)->findOrFail($widgetId);
            $duplicatedWidget = $originalWidget->duplicate();
            
            return $duplicatedWidget->load(['aiProvider', 'aiModel']);
        });
    }

    /**
     * Generate embed code for a widget
     */
    public function generateEmbedCode(string $widgetId, string $type, string $userId): string
    {
        $widget = Widget::forUser($userId)->findOrFail($widgetId);
        
        if (!$widget->is_published) {
            throw new \Exception('Widget must be published to generate embed code.');
        }

        $baseUrl = config('app.url');
        $embedUrl = $widget->getEmbedUrl();

        switch ($type) {
            case 'iframe':
                return $this->generateIframeCode($widget, $embedUrl);
            case 'script':
                return $this->generateScriptCode($widget, $baseUrl);
            case 'react':
                return $this->generateReactCode($widget, $baseUrl);
            case 'vue':
                return $this->generateVueCode($widget, $baseUrl);
            case 'wordpress':
                return $this->generateWordPressCode($widget, $embedUrl);
            default:
                throw new \Exception('Unsupported embed type.');
        }
    }

    /**
     * Get preview URL for a widget
     */
    public function getPreviewUrl(string $widgetId, string $userId): string
    {
        $widget = Widget::forUser($userId)->findOrFail($widgetId);
        return $widget->getPreviewUrl();
    }

    /**
     * Get widget analytics
     */
    public function getWidgetAnalytics(string $widgetId, string $userId, array $filters = []): array
    {
        $widget = Widget::forUser($userId)->findOrFail($widgetId);
        
        $startDate = $filters['start_date'] ?? now()->subDays(30)->toDateString();
        $endDate = $filters['end_date'] ?? now()->toDateString();
        $granularity = $filters['granularity'] ?? 'day';

        $analytics = WidgetAnalytics::where('widget_id', $widgetId)
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date')
            ->get();

        return [
            'widget_id' => $widgetId,
            'total_conversations' => $analytics->sum('total_conversations'),
            'total_messages' => $analytics->sum('total_messages'),
            'avg_response_time' => $analytics->avg('avg_response_time'),
            'user_satisfaction' => $analytics->avg('user_satisfaction'),
            'most_common_queries' => $this->getMostCommonQueries($analytics),
            'daily_stats' => $analytics->map(function ($item) {
                return [
                    'date' => $item->date,
                    'conversations' => $item->total_conversations,
                    'messages' => $item->total_messages,
                ];
            })->toArray()
        ];
    }

    /**
     * Generate iframe embed code
     */
    private function generateIframeCode(Widget $widget, string $embedUrl): string
    {
        return sprintf(
            '<iframe src="%s" width="%d" height="%d" frameborder="0" style="border: none; border-radius: %dpx;"></iframe>',
            $embedUrl,
            $widget->width,
            $widget->height,
            $widget->border_radius
        );
    }

    /**
     * Generate JavaScript embed code
     */
    private function generateScriptCode(Widget $widget, string $baseUrl): string
    {
        return sprintf(
            "<script>
(function(w,d,s,o,f,js,fjs){
w['ChatSupportWidget']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};
js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
}(window,document,'script','csw','%s/widget.js'));
csw('init', '%s');
</script>",
            $baseUrl,
            $widget->id
        );
    }

    /**
     * Generate React embed code
     */
    private function generateReactCode(Widget $widget, string $baseUrl): string
    {
        return sprintf(
            "import { ChatSupportWidget } from '@chatsupport/react';

function App() {
  return (
    <div>
      {/* Your app content */}
      <ChatSupportWidget 
        widgetId=\"%s\"
        apiUrl=\"%s\"
      />
    </div>
  );
}",
            $widget->id,
            $baseUrl
        );
    }

    /**
     * Generate Vue embed code
     */
    private function generateVueCode(Widget $widget, string $baseUrl): string
    {
        return sprintf(
            "<template>
  <div>
    <!-- Your app content -->
    <ChatSupportWidget 
      :widget-id=\"'%s'\"
      :api-url=\"'%s'\"
    />
  </div>
</template>

<script>
import { ChatSupportWidget } from '@chatsupport/vue';

export default {
  components: {
    ChatSupportWidget
  }
};
</script>",
            $widget->id,
            $baseUrl
        );
    }

    /**
     * Generate WordPress embed code
     */
    private function generateWordPressCode(Widget $widget, string $embedUrl): string
    {
        return sprintf(
            '[chat_widget id="%s" width="%d" height="%d"]',
            $widget->id,
            $widget->width,
            $widget->height
        );
    }

    /**
     * Get most common queries from analytics
     */
    private function getMostCommonQueries($analytics): array
    {
        $allQueries = [];
        
        foreach ($analytics as $analytic) {
            if ($analytic->popular_queries) {
                $allQueries = array_merge($allQueries, $analytic->popular_queries);
            }
        }
        
        $queryCounts = array_count_values(array_column($allQueries, 'query'));
        arsort($queryCounts);
        
        return array_slice(
            array_map(fn($query, $count) => ['query' => $query, 'count' => $count], 
                     array_keys($queryCounts), 
                     array_values($queryCounts)
            ), 
            0, 
            10
        );
    }
}
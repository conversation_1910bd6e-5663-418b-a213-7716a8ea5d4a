<?php

namespace App\Services;

use App\Models\AiProvider;
use App\Models\AiModel;
use App\Models\UserAiProviderConfig;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AiProviderService
{
    /**
     * Get all AI providers with user configuration status
     */
    public function getProvidersForUser(string $userId): Collection
    {
        return AiProvider::active()
            ->with(['models' => function ($query) {
                $query->available()->orderBy('sort_order')->orderBy('name');
            }])
            ->get()
            ->map(function ($provider) use ($userId) {
                $userConfig = $provider->getUserConfig(auth()->user());
                
                return [
                    'id' => $provider->id,
                    'name' => $provider->name,
                    'slug' => $provider->slug,
                    'description' => $provider->description,
                    'is_configured' => $userConfig ? $userConfig->is_configured : false,
                    'status' => $userConfig ? $userConfig->status : 'inactive',
                    'models' => $provider->models->map(function ($model) {
                        return [
                            'id' => $model->id,
                            'name' => $model->name,
                            'model_id' => $model->model_id,
                            'provider' => $model->aiProvider->name,
                            'description' => $model->description,
                            'max_tokens' => $model->max_tokens,
                            'supports_streaming' => $model->supports_streaming,
                            'is_available' => $model->is_available,
                        ];
                    }),
                    'last_tested_at' => $userConfig?->last_tested_at,
                ];
            });
    }

    /**
     * Get a specific AI provider with user configuration
     */
    public function getProvider(string $providerId, string $userId): array
    {
        $provider = AiProvider::with(['models' => function ($query) {
            $query->available()->orderBy('sort_order')->orderBy('name');
        }])->findOrFail($providerId);
        
        $userConfig = UserAiProviderConfig::where('user_id', $userId)
            ->where('ai_provider_id', $providerId)
            ->first();

        return [
            'id' => $provider->id,
            'name' => $provider->name,
            'slug' => $provider->slug,
            'description' => $provider->description,
            'is_configured' => $userConfig ? $userConfig->is_configured : false,
            'status' => $userConfig ? $userConfig->status : 'inactive',
            'models' => $provider->models->map(function ($model) {
                return [
                    'id' => $model->id,
                    'name' => $model->name,
                    'model_id' => $model->model_id,
                    'provider' => $model->aiProvider->name,
                    'description' => $model->description,
                    'max_tokens' => $model->max_tokens,
                    'supports_streaming' => $model->supports_streaming,
                    'is_available' => $model->is_available,
                ];
            }),
            'configuration' => $userConfig?->configuration,
            'last_tested_at' => $userConfig?->last_tested_at,
            'test_results' => $userConfig?->test_results,
        ];
    }

    /**
     * Update AI provider configuration for a user
     */
    public function updateProviderConfig(string $providerId, string $userId, array $data): array
    {
        $provider = AiProvider::findOrFail($providerId);
        
        $userConfig = UserAiProviderConfig::updateOrCreate(
            [
                'user_id' => $userId,
                'ai_provider_id' => $providerId,
            ],
            [
                'api_key' => $data['api_key'] ?? null,
                'configuration' => $data['settings'] ?? [],
                'is_configured' => !empty($data['api_key']),
                'status' => !empty($data['api_key']) ? 'active' : 'inactive',
            ]
        );

        return $this->getProvider($providerId, $userId);
    }

    /**
     * Test AI provider connection
     */
    public function testProvider(string $providerId, string $userId): array
    {
        $provider = AiProvider::findOrFail($providerId);
        $userConfig = UserAiProviderConfig::where('user_id', $userId)
            ->where('ai_provider_id', $providerId)
            ->firstOrFail();

        if (!$userConfig->api_key) {
            throw new \Exception('API key not configured for this provider.');
        }

        try {
            $result = $this->performProviderTest($provider, $userConfig);
            
            $userConfig->updateTestResults($result);
            
            return $result;
        } catch (\Exception $e) {
            $errorResult = [
                'success' => false,
                'error' => $e->getMessage(),
                'tested_at' => now()->toISOString(),
            ];
            
            $userConfig->updateTestResults($errorResult);
            
            return $errorResult;
        }
    }

    /**
     * Fetch available models from AI provider
     */
    public function fetchModelsFromProvider(string $providerId, string $userId): Collection
    {
        $provider = AiProvider::findOrFail($providerId);
        $userConfig = UserAiProviderConfig::where('user_id', $userId)
            ->where('ai_provider_id', $providerId)
            ->firstOrFail();

        if (!$userConfig->api_key) {
            throw new \Exception('API key not configured for this provider.');
        }

        try {
            $models = $this->fetchModelsFromAPI($provider, $userConfig);
            
            // Update or create models in database
            foreach ($models as $modelData) {
                AiModel::updateOrCreate(
                    [
                        'ai_provider_id' => $providerId,
                        'model_id' => $modelData['id'],
                    ],
                    [
                        'name' => $modelData['name'],
                        'description' => $modelData['description'] ?? null,
                        'max_tokens' => $modelData['max_tokens'] ?? 4096,
                        'context_length' => $modelData['context_length'] ?? 4096,
                        'supports_streaming' => $modelData['supports_streaming'] ?? false,
                        'supports_functions' => $modelData['supports_functions'] ?? false,
                        'supports_vision' => $modelData['supports_vision'] ?? false,
                        'input_cost_per_1k_tokens' => $modelData['input_cost'] ?? null,
                        'output_cost_per_1k_tokens' => $modelData['output_cost'] ?? null,
                        'is_available' => true,
                    ]
                );
            }

            return $provider->fresh()->getAvailableModels();
        } catch (\Exception $e) {
            Log::error('Failed to fetch models from provider', [
                'provider_id' => $providerId,
                'user_id' => $userId,
                'error' => $e->getMessage(),
            ]);
            
            throw new \Exception('Failed to fetch models: ' . $e->getMessage());
        }
    }

    /**
     * Get available AI models for a user
     */
    public function getAvailableModels(string $userId, ?string $providerId = null): Collection
    {
        $query = AiModel::with('aiProvider')
            ->whereHas('aiProvider', function ($q) use ($userId) {
                $q->whereHas('userConfigs', function ($subQ) use ($userId) {
                    $subQ->where('user_id', $userId)
                         ->where('is_configured', true)
                         ->where('status', 'active');
                });
            })
            ->available();

        if ($providerId) {
            $query->where('ai_provider_id', $providerId);
        }

        return $query->orderBy('sort_order')
            ->orderBy('name')
            ->get()
            ->map(function ($model) {
                return [
                    'id' => $model->model_id,
                    'name' => $model->name,
                    'provider' => $model->aiProvider->name,
                    'description' => $model->description,
                    'max_tokens' => $model->max_tokens,
                    'supports_streaming' => $model->supports_streaming,
                    'cost_per_token' => $model->input_cost_per_1k_tokens,
                    'is_available' => $model->is_available,
                ];
            });
    }

    /**
     * Perform actual provider test
     */
    private function performProviderTest(AiProvider $provider, UserAiProviderConfig $userConfig): array
    {
        $headers = $provider->getAuthHeaders($userConfig->getDecryptedApiKey());
        
        // Test endpoint varies by provider
        $testEndpoint = $this->getTestEndpoint($provider);
        $testPayload = $this->getTestPayload($provider);

        $response = Http::withHeaders($headers)
            ->timeout(30)
            ->post($provider->base_url . $testEndpoint, $testPayload);

        if ($response->successful()) {
            return [
                'success' => true,
                'message' => 'Connection successful',
                'response_time' => $response->transferStats?->getTransferTime() ?? 0,
                'tested_at' => now()->toISOString(),
            ];
        } else {
            throw new \Exception('API request failed: ' . $response->body());
        }
    }

    /**
     * Fetch models from provider API
     */
    private function fetchModelsFromAPI(AiProvider $provider, UserAiProviderConfig $userConfig): array
    {
        $headers = $provider->getAuthHeaders($userConfig->getDecryptedApiKey());
        
        $modelsEndpoint = $this->getModelsEndpoint($provider);
        
        $response = Http::withHeaders($headers)
            ->timeout(30)
            ->get($provider->base_url . $modelsEndpoint);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch models: ' . $response->body());
        }

        return $this->parseModelsResponse($provider, $response->json());
    }

    /**
     * Get test endpoint for provider
     */
    private function getTestEndpoint(AiProvider $provider): string
    {
        return match ($provider->slug) {
            'openai' => '/v1/models',
            'groq' => '/openai/v1/models',
            'openrouter' => '/api/v1/models',
            'gemini' => '/v1/models',
            'mistral' => '/v1/models',
            'deepseek' => '/v1/models',
            default => '/v1/models',
        };
    }

    /**
     * Get test payload for provider
     */
    private function getTestPayload(AiProvider $provider): array
    {
        // Most providers support a simple models list request for testing
        return [];
    }

    /**
     * Get models endpoint for provider
     */
    private function getModelsEndpoint(AiProvider $provider): string
    {
        return match ($provider->slug) {
            'openai' => '/v1/models',
            'groq' => '/openai/v1/models',
            'openrouter' => '/api/v1/models',
            'gemini' => '/v1/models',
            'mistral' => '/v1/models',
            'deepseek' => '/v1/models',
            default => '/v1/models',
        };
    }

    /**
     * Parse models response from provider
     */
    private function parseModelsResponse(AiProvider $provider, array $response): array
    {
        $models = $response['data'] ?? $response['models'] ?? $response;
        
        return collect($models)->map(function ($model) use ($provider) {
            return [
                'id' => $model['id'] ?? $model['name'],
                'name' => $model['name'] ?? $model['id'],
                'description' => $model['description'] ?? null,
                'max_tokens' => $model['max_tokens'] ?? $model['context_length'] ?? 4096,
                'context_length' => $model['context_length'] ?? $model['max_tokens'] ?? 4096,
                'supports_streaming' => $model['supports_streaming'] ?? true,
                'supports_functions' => $model['supports_functions'] ?? false,
                'supports_vision' => $model['supports_vision'] ?? false,
                'input_cost' => $model['pricing']['input'] ?? null,
                'output_cost' => $model['pricing']['output'] ?? null,
            ];
        })->toArray();
    }
}

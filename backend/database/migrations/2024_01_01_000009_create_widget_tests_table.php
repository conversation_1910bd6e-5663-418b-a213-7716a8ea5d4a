<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('widget_tests', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('widget_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('test_messages');
            $table->json('expected_responses')->nullable();
            $table->enum('status', ['pending', 'running', 'completed', 'failed'])->default('pending');
            $table->json('results')->nullable();
            $table->boolean('passed')->nullable();
            $table->decimal('score', 5, 2)->nullable();
            $table->text('details')->nullable();
            $table->timestamp('last_run_at')->nullable();
            $table->decimal('execution_time', 8, 2)->nullable();
            $table->timestamps();
            
            $table->index(['widget_id', 'status']);
            $table->index('last_run_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('widget_tests');
    }
};

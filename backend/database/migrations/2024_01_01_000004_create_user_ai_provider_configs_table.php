<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_ai_provider_configs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('ai_provider_id')->constrained()->cascadeOnDelete();
            $table->string('api_key')->nullable();
            $table->json('configuration')->nullable();
            $table->boolean('is_configured')->default(false);
            $table->enum('status', ['active', 'inactive', 'error'])->default('inactive');
            $table->timestamp('last_tested_at')->nullable();
            $table->json('test_results')->nullable();
            $table->string('error_message')->nullable();
            $table->timestamps();
            
            $table->unique(['user_id', 'ai_provider_id']);
            $table->index(['user_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_ai_provider_configs');
    }
};

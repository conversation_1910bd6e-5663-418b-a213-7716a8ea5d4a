<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('widget_conversations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('widget_id')->constrained()->cascadeOnDelete();
            $table->string('session_id');
            $table->string('visitor_id')->nullable();
            $table->ipAddress('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->string('referrer')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->enum('status', ['active', 'ended', 'abandoned'])->default('active');
            $table->timestamp('started_at');
            $table->timestamp('ended_at')->nullable();
            $table->integer('message_count')->default(0);
            $table->decimal('avg_response_time', 8, 2)->nullable();
            $table->integer('satisfaction_rating')->nullable();
            $table->text('feedback')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            $table->index(['widget_id', 'status']);
            $table->index(['session_id', 'widget_id']);
            $table->index('started_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('widget_conversations');
    }
};

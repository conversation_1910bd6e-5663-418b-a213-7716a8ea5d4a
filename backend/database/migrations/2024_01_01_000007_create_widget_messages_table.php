<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('widget_messages', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('conversation_id')->constrained('widget_conversations')->cascadeOnDelete();
            $table->foreignUuid('widget_id')->constrained()->cascadeOnDelete();
            $table->enum('role', ['user', 'assistant', 'system']);
            $table->text('content');
            $table->json('metadata')->nullable();
            $table->string('ai_model_used')->nullable();
            $table->integer('tokens_used')->nullable();
            $table->decimal('response_time', 8, 2)->nullable();
            $table->decimal('cost', 10, 6)->nullable();
            $table->boolean('is_error')->default(false);
            $table->text('error_message')->nullable();
            $table->json('attachments')->nullable();
            $table->timestamp('sent_at');
            $table->timestamps();
            
            $table->index(['conversation_id', 'role']);
            $table->index(['widget_id', 'sent_at']);
            $table->index('sent_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('widget_messages');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('widget_analytics', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('widget_id')->constrained()->cascadeOnDelete();
            $table->date('date');
            $table->integer('total_conversations')->default(0);
            $table->integer('total_messages')->default(0);
            $table->integer('unique_visitors')->default(0);
            $table->decimal('avg_response_time', 8, 2)->nullable();
            $table->decimal('avg_conversation_duration', 8, 2)->nullable();
            $table->decimal('user_satisfaction', 3, 2)->nullable();
            $table->integer('total_tokens_used')->default(0);
            $table->decimal('total_cost', 10, 6)->default(0);
            $table->json('popular_queries')->nullable();
            $table->json('error_stats')->nullable();
            $table->json('device_stats')->nullable();
            $table->json('location_stats')->nullable();
            $table->timestamps();
            
            $table->unique(['widget_id', 'date']);
            $table->index(['widget_id', 'date']);
            $table->index('date');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('widget_analytics');
    }
};

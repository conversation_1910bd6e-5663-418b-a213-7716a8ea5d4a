<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ai_providers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->string('base_url');
            $table->json('headers')->nullable();
            $table->json('default_parameters')->nullable();
            $table->boolean('api_key_required')->default(true);
            $table->string('api_key_header')->default('Authorization');
            $table->string('api_key_prefix')->default('Bearer');
            $table->enum('status', ['active', 'inactive', 'maintenance'])->default('active');
            $table->boolean('supports_streaming')->default(false);
            $table->boolean('supports_functions')->default(false);
            $table->boolean('supports_vision')->default(false);
            $table->integer('rate_limit_per_minute')->default(60);
            $table->integer('rate_limit_per_hour')->default(1000);
            $table->decimal('cost_per_1k_tokens', 10, 6)->nullable();
            $table->json('supported_formats')->nullable();
            $table->json('configuration_schema')->nullable();
            $table->timestamp('last_tested_at')->nullable();
            $table->json('test_results')->nullable();
            $table->timestamps();
            
            $table->index(['slug', 'status']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ai_providers');
    }
};

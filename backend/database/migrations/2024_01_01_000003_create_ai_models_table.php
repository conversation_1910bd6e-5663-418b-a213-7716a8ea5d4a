<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('ai_models', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('ai_provider_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->string('model_id');
            $table->text('description')->nullable();
            $table->integer('max_tokens')->default(4096);
            $table->integer('context_length')->default(4096);
            $table->boolean('supports_streaming')->default(false);
            $table->boolean('supports_functions')->default(false);
            $table->boolean('supports_vision')->default(false);
            $table->boolean('supports_json_mode')->default(false);
            $table->decimal('input_cost_per_1k_tokens', 10, 6)->nullable();
            $table->decimal('output_cost_per_1k_tokens', 10, 6)->nullable();
            $table->boolean('is_available')->default(true);
            $table->boolean('is_deprecated')->default(false);
            $table->json('capabilities')->nullable();
            $table->json('parameters')->nullable();
            $table->integer('sort_order')->default(0);
            $table->timestamp('deprecated_at')->nullable();
            $table->timestamps();
            
            $table->index(['ai_provider_id', 'is_available']);
            $table->index(['model_id', 'ai_provider_id']);
            $table->index('is_available');
            $table->unique(['ai_provider_id', 'model_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('ai_models');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('widgets', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('user_id')->constrained()->cascadeOnDelete();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('version')->default('1.0.0');
            $table->boolean('is_active')->default(true);
            $table->boolean('is_published')->default(false);
            $table->boolean('is_draft')->default(true);
            $table->timestamp('published_at')->nullable();
            $table->json('tags')->nullable();
            $table->string('category')->nullable();
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            
            // Basic Settings
            $table->string('ai_provider');
            $table->string('ai_model');
            $table->text('system_prompt')->default('You are a helpful customer support assistant.');
            $table->decimal('temperature', 3, 2)->default(0.70);
            $table->integer('max_tokens')->default(1000);
            $table->integer('response_timeout')->default(10000);
            
            // Styling Configuration
            $table->string('primary_color')->default('#4f46e5');
            $table->string('secondary_color')->default('#ffffff');
            $table->string('background_color')->default('#f8fafc');
            $table->string('text_color')->default('#1f2937');
            $table->string('accent_color')->default('#10b981');
            $table->string('error_color')->default('#ef4444');
            $table->string('font_family')->default('Inter');
            $table->integer('font_size')->default(14);
            $table->string('font_weight')->default('normal');
            $table->integer('border_radius')->default(8);
            $table->string('shadow_intensity')->default('md');
            $table->string('animation')->default('fade');
            $table->text('custom_css')->nullable();
            
            // Behavior Settings
            $table->boolean('auto_open')->default(false);
            $table->integer('auto_open_delay')->default(3000);
            $table->string('auto_open_trigger')->default('time');
            $table->integer('scroll_trigger_percent')->default(50);
            $table->string('welcome_message')->default('Hello! How can I help you today?');
            $table->string('placeholder_text')->default('Type your message here...');
            $table->boolean('show_typing_indicator')->default(true);
            $table->integer('typing_indicator_delay')->default(1500);
            $table->boolean('enable_sound_notifications')->default(false);
            $table->decimal('sound_volume', 3, 2)->default(0.50);
            $table->boolean('show_branding')->default(true);
            $table->boolean('enable_file_upload')->default(false);
            $table->integer('max_file_size')->default(5);
            $table->json('allowed_file_types')->default(json_encode(['image/jpeg', 'image/png', 'application/pdf']));
            $table->boolean('enable_emojis')->default(true);
            $table->boolean('enable_markdown')->default(false);
            $table->json('conversation_starters')->nullable();
            
            // Positioning Settings
            $table->string('position')->default('bottom-right');
            $table->integer('offset_x')->default(20);
            $table->integer('offset_y')->default(20);
            $table->integer('width')->default(350);
            $table->integer('height')->default(600);
            $table->integer('min_width')->default(300);
            $table->integer('min_height')->default(400);
            $table->integer('max_width')->default(500);
            $table->integer('max_height')->default(700);
            $table->integer('z_index')->default(9999);
            $table->string('icon_style')->default('chat');
            $table->integer('icon_size')->default(60);
            $table->integer('mobile_breakpoint')->default(768);
            $table->integer('tablet_breakpoint')->default(1024);
            $table->boolean('responsive_scaling')->default(true);
            
            // Advanced Settings
            $table->json('allowed_domains')->nullable();
            $table->json('blocked_domains')->nullable();
            $table->integer('rate_limit_per_minute')->default(20);
            $table->integer('rate_limit_per_hour')->default(200);
            $table->boolean('enable_analytics')->default(true);
            $table->string('analytics_provider')->default('google');
            $table->text('custom_analytics_code')->nullable();
            $table->boolean('enable_a11y')->default(true);
            $table->boolean('keyboard_navigation')->default(true);
            $table->boolean('screen_reader_support')->default(true);
            $table->boolean('high_contrast_mode')->default(false);
            $table->boolean('enable_gdpr')->default(false);
            $table->text('gdpr_message')->nullable();
            $table->boolean('cookie_consent')->default(false);
            $table->integer('data_retention_days')->default(30);
            $table->boolean('enable_encryption')->default(true);
            $table->string('webhook_url')->nullable();
            $table->json('webhook_events')->nullable();
            $table->json('custom_headers')->nullable();
            $table->boolean('enable_cors')->default(true);
            $table->json('cors_origins')->default(json_encode(['*']));
            
            // Device Preview Settings
            $table->string('current_device')->default('desktop');
            $table->integer('custom_width')->nullable();
            $table->integer('custom_height')->nullable();
            $table->string('orientation')->default('portrait');
            $table->boolean('show_device_frame')->default(true);
            $table->decimal('device_scale', 3, 2)->default(1.00);
            
            // Testing Configuration
            $table->boolean('enable_testing')->default(false);
            $table->json('test_scenarios')->nullable();
            $table->boolean('performance_monitoring')->default(true);
            $table->boolean('error_tracking')->default(true);
            $table->boolean('load_testing')->default(false);
            $table->integer('max_concurrent_users')->default(100);
            
            // Metadata
            $table->string('embed_code_hash')->nullable();
            $table->integer('total_conversations')->default(0);
            $table->integer('total_messages')->default(0);
            $table->decimal('avg_response_time', 8, 2)->nullable();
            $table->decimal('user_satisfaction', 3, 2)->nullable();
            $table->timestamp('last_activity_at')->nullable();
            
            $table->timestamps();
            
            $table->index(['user_id', 'is_published']);
            $table->index(['is_published', 'is_active']);
            $table->index(['ai_provider', 'ai_model']);
            $table->index('category');
            $table->index('priority');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('widgets');
    }
};

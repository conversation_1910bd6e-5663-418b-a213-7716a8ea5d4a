<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\WidgetController;
use App\Http\Controllers\Api\V1\AiProviderController;
use App\Http\Controllers\Api\V1\AuthController;

// Public authentication routes
Route::prefix('v1/auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('quick-admin-login', [AuthController::class, 'quickAdminLogin']);
});

// Protected routes
Route::prefix('v1')->middleware('auth:sanctum')->group(function () {
    // Authentication routes
    Route::post('auth/logout', [AuthController::class, 'logout']);
    Route::get('auth/me', [AuthController::class, 'me']);
    Route::post('auth/refresh', [AuthController::class, 'refresh']);
    Route::post('auth/revoke-all', [AuthController::class, 'revokeAll']);
    
    // Widget routes
    Route::apiResource('widgets', WidgetController::class);
    Route::post('widgets/{id}/publish', [WidgetController::class, 'publish']);
    Route::post('widgets/{id}/unpublish', [WidgetController::class, 'unpublish']);
    Route::post('widgets/{id}/duplicate', [WidgetController::class, 'duplicate']);
    Route::get('widgets/{id}/embed/{type}', [WidgetController::class, 'embed']);
    Route::get('widgets/{id}/preview', [WidgetController::class, 'preview']);
    Route::get('widgets/{id}/analytics', [WidgetController::class, 'analytics']);
    
    // AI Provider routes
    Route::get('ai-providers', [AiProviderController::class, 'index']);
    Route::get('ai-providers/{id}', [AiProviderController::class, 'show']);
    Route::put('ai-providers/{id}', [AiProviderController::class, 'update']);
    Route::post('ai-providers/{id}/test', [AiProviderController::class, 'test']);
    Route::post('ai-providers/{id}/fetch-models', [AiProviderController::class, 'fetchModels']);
    Route::get('ai-models', [AiProviderController::class, 'models']);
}); 
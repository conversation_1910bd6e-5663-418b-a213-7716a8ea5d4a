import React, { useEffect, useRef, useState, useCallback } from 'react';
import { WidgetConfig } from '@/types/widget-config';
import { useWidgetConfigStore } from '@/lib/widget-config-store';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut, 
  Maximize2,
  RefreshCw,
  Eye,
  EyeOff,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface WidgetPreviewProps {
  config: WidgetConfig;
  className?: string;
}

interface DevicePreset {
  name: string;
  width: number;
  height: number;
  icon: React.ComponentType<{ className?: string }>;
  userAgent?: string;
}

const devicePresets: Record<string, DevicePreset> = {
  desktop: {
    name: 'Desktop',
    width: 1920,
    height: 1080,
    icon: Monitor
  },
  tablet: {
    name: 'Tablet',
    width: 768,
    height: 1024,
    icon: Tablet,
    userAgent: 'Mozilla/5.0 (iPad; CPU OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  },
  mobile: {
    name: 'Mobile',
    width: 375,
    height: 667,
    icon: Smartphone,
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
  }
};

const WidgetPreview: React.FC<WidgetPreviewProps> = ({ config, className }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInteractive, setIsInteractive] = useState(true);
  const [showDeviceFrame, setShowDeviceFrame] = useState(true);
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
  
  const {
    previewDevice,
    previewScale,
    setPreviewDevice,
    setPreviewScale
  } = useWidgetConfigStore();

  const currentDevice = devicePresets[previewDevice];
  const isPortrait = orientation === 'portrait';
  const deviceWidth = isPortrait ? currentDevice.width : currentDevice.height;
  const deviceHeight = isPortrait ? currentDevice.height : currentDevice.width;

  // Generate widget HTML for iframe
  const generateWidgetHTML = useCallback((config: WidgetConfig): string => {
    const { styling, positioning, behavior, basic } = config;
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Widget Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: ${styling.fontFamily}, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }
        
        .mock-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .mock-header {
            background: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .mock-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .mock-text {
            height: 1rem;
            background: #e5e7eb;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }
        
        .mock-text.short { width: 60%; }
        .mock-text.medium { width: 80%; }
        .mock-text.long { width: 100%; }
        
        /* Widget Styles */
        .chat-widget {
            position: fixed;
            z-index: ${positioning.zIndex};
            font-family: ${styling.fontFamily}, sans-serif;
            font-size: ${styling.fontSize}px;
            font-weight: ${styling.fontWeight};
        }
        
        .widget-button {
            width: ${positioning.iconSize}px;
            height: ${positioning.iconSize}px;
            background: ${styling.primaryColor};
            border: none;
            border-radius: ${styling.borderRadius}px;
            color: ${styling.secondaryColor};
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
            position: relative;
        }
        
        .widget-button:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        .widget-button.animate-bounce {
            animation: bounce 2s infinite;
        }
        
        .widget-button.animate-pulse {
            animation: pulse 2s infinite;
        }
        
        .widget-chat {
            width: ${positioning.width}px;
            height: ${positioning.height}px;
            background: ${styling.backgroundColor};
            border-radius: ${styling.borderRadius}px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
            display: none;
            flex-direction: column;
            overflow: hidden;
            position: absolute;
            bottom: ${positioning.iconSize + 10}px;
        }
        
        .widget-chat.open {
            display: flex;
            animation: slideUp 0.3s ease-out;
        }
        
        .chat-header {
            background: ${styling.primaryColor};
            color: ${styling.secondaryColor};
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: ${styling.backgroundColor};
        }
        
        .message {
            margin-bottom: 1rem;
            max-width: 80%;
        }
        
        .message.bot {
            align-self: flex-start;
        }
        
        .message.user {
            align-self: flex-end;
            margin-left: auto;
        }
        
        .message-bubble {
            padding: 0.75rem 1rem;
            border-radius: ${styling.borderRadius}px;
            word-wrap: break-word;
        }
        
        .message.bot .message-bubble {
            background: #f3f4f6;
            color: ${styling.textColor};
        }
        
        .message.user .message-bubble {
            background: ${styling.primaryColor};
            color: ${styling.secondaryColor};
        }
        
        .chat-input {
            padding: 1rem;
            border-top: 1px solid #e5e7eb;
            background: ${styling.backgroundColor};
        }
        
        .input-container {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }
        
        .message-input {
            flex: 1;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: ${styling.borderRadius}px;
            font-size: ${styling.fontSize}px;
            font-family: inherit;
            outline: none;
        }
        
        .message-input:focus {
            border-color: ${styling.primaryColor};
            box-shadow: 0 0 0 3px ${styling.primaryColor}20;
        }
        
        .send-button {
            padding: 0.75rem 1rem;
            background: ${styling.primaryColor};
            color: ${styling.secondaryColor};
            border: none;
            border-radius: ${styling.borderRadius}px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .send-button:hover {
            opacity: 0.9;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.5rem 1rem;
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .typing-dots {
            display: flex;
            gap: 0.25rem;
        }
        
        .typing-dot {
            width: 4px;
            height: 4px;
            background: #6b7280;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        /* Positioning */
        .position-bottom-right {
            bottom: ${positioning.offsetY}px;
            right: ${positioning.offsetX}px;
        }
        
        .position-bottom-left {
            bottom: ${positioning.offsetY}px;
            left: ${positioning.offsetX}px;
        }
        
        .position-top-right {
            top: ${positioning.offsetY}px;
            right: ${positioning.offsetX}px;
        }
        
        .position-top-left {
            top: ${positioning.offsetY}px;
            left: ${positioning.offsetX}px;
        }
        
        /* Animations */
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
            40%, 43% { transform: translate3d(0,-8px,0); }
            70% { transform: translate3d(0,-4px,0); }
            90% { transform: translate3d(0,-2px,0); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        /* Responsive */
        @media (max-width: ${positioning.mobileBreakpoint}px) {
            .widget-chat {
                width: calc(100vw - 2rem);
                height: calc(100vh - 2rem);
                position: fixed;
                top: 1rem;
                left: 1rem;
                right: 1rem;
                bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Mock Website Content -->
    <div class="mock-content">
        <div class="mock-header">
            <div class="mock-text medium"></div>
        </div>
        
        <div class="mock-section">
            <div class="mock-text short"></div>
            <div class="mock-text long"></div>
            <div class="mock-text medium"></div>
            <div class="mock-text long"></div>
        </div>
        
        <div class="mock-section">
            <div class="mock-text medium"></div>
            <div class="mock-text short"></div>
            <div class="mock-text long"></div>
        </div>
        
        <div class="mock-section">
            <div class="mock-text long"></div>
            <div class="mock-text medium"></div>
            <div class="mock-text short"></div>
            <div class="mock-text long"></div>
        </div>
    </div>
    
    <!-- Chat Widget -->
    <div class="chat-widget position-${positioning.position}">
        <button class="widget-button ${styling.animation !== 'none' ? `animate-${styling.animation}` : ''}" onclick="toggleChat()">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                ${getIconSVG(positioning.iconStyle)}
            </svg>
        </button>
        
        <div class="widget-chat" id="chatWindow">
            <div class="chat-header">
                <div>
                    <div style="font-weight: 600;">${basic.name || 'Support Chat'}</div>
                    <div style="font-size: 0.875rem; opacity: 0.9;">We're here to help!</div>
                </div>
                <button onclick="toggleChat()" style="background: none; border: none; color: inherit; cursor: pointer;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <div class="message-bubble">
                        ${behavior.welcomeMessage}
                    </div>
                </div>
                ${behavior.conversationStarters.length > 0 ? `
                <div class="conversation-starters" style="margin-top: 1rem;">
                    ${behavior.conversationStarters.map(starter => `
                        <button onclick="sendMessage('${starter}')" style="
                            display: block;
                            width: 100%;
                            text-align: left;
                            padding: 0.5rem;
                            margin-bottom: 0.5rem;
                            background: #f9fafb;
                            border: 1px solid #e5e7eb;
                            border-radius: ${styling.borderRadius}px;
                            cursor: pointer;
                            font-size: 0.875rem;
                        ">${starter}</button>
                    `).join('')}
                </div>` : ''}
            </div>
            
            ${behavior.showTypingIndicator ? `
            <div class="typing-indicator" id="typingIndicator" style="display: none;">
                <span>AI is typing</span>
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>` : ''}
            
            <div class="chat-input">
                <div class="input-container">
                    <input 
                        type="text" 
                        class="message-input" 
                        placeholder="${behavior.placeholderText}"
                        onkeypress="handleKeyPress(event)"
                        id="messageInput"
                    >
                    <button class="send-button" onclick="sendMessage()">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="22" y1="2" x2="11" y2="13"></line>
                            <polygon points="22,2 15,22 11,13 2,9"></polygon>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        let chatOpen = false;
        
        function toggleChat() {
            const chatWindow = document.getElementById('chatWindow');
            chatOpen = !chatOpen;
            
            if (chatOpen) {
                chatWindow.classList.add('open');
            } else {
                chatWindow.classList.remove('open');
            }
            
            // Notify parent window of state change
            window.parent.postMessage({
                type: 'WIDGET_STATE_CHANGE',
                payload: { isOpen: chatOpen }
            }, '*');
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function sendMessage(message) {
            const input = document.getElementById('messageInput');
            const messagesContainer = document.getElementById('chatMessages');
            const typingIndicator = document.getElementById('typingIndicator');
            
            const messageText = message || input.value.trim();
            if (!messageText) return;
            
            // Add user message
            const userMessage = document.createElement('div');
            userMessage.className = 'message user';
            userMessage.innerHTML = \`<div class="message-bubble">\${messageText}</div>\`;
            messagesContainer.appendChild(userMessage);
            
            // Clear input
            if (!message) input.value = '';
            
            // Show typing indicator
            if (typingIndicator) {
                typingIndicator.style.display = 'flex';
            }
            
            // Scroll to bottom
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            // Simulate AI response
            setTimeout(() => {
                if (typingIndicator) {
                    typingIndicator.style.display = 'none';
                }
                
                const botMessage = document.createElement('div');
                botMessage.className = 'message bot';
                botMessage.innerHTML = \`<div class="message-bubble">Thanks for your message! This is a preview of how the AI would respond.</div>\`;
                messagesContainer.appendChild(botMessage);
                
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, ${behavior.typingIndicatorDelay});
            
            // Notify parent window of message
            window.parent.postMessage({
                type: 'WIDGET_MESSAGE_SENT',
                payload: { message: messageText }
            }, '*');
        }
        
        // Auto-open functionality
        ${behavior.autoOpen ? `
        setTimeout(() => {
            if (!chatOpen) {
                toggleChat();
            }
        }, ${behavior.autoOpenDelay});
        ` : ''}
        
        // Listen for config updates from parent
        window.addEventListener('message', (event) => {
            if (event.data.type === 'UPDATE_WIDGET_CONFIG') {
                // Reload the iframe with new config
                window.location.reload();
            }
        });
        
        // Notify parent that widget is ready
        window.parent.postMessage({
            type: 'WIDGET_READY',
            payload: { timestamp: Date.now() }
        }, '*');
    </script>
</body>
</html>`;
  }, []);

  // Helper function to get icon SVG
  const getIconSVG = (iconStyle: string): string => {
    switch (iconStyle) {
      case 'chat':
        return '<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>';
      case 'message':
        return '<path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline>';
      case 'question':
        return '<circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><line x1="12" y1="17" x2="12.01" y2="17"></line>';
      case 'support':
        return '<path d="M14 9V5a3 3 0 0 0-6 0v4"></path><path d="M3 9h18l-2 9H5l-2-9Z"></path>';
      case 'help':
        return '<circle cx="12" cy="12" r="10"></circle><path d="M8 12h8"></path><path d="M12 8v8"></path>';
      default:
        return '<path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>';
    }
  };

  // Update iframe content when config changes
  useEffect(() => {
    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const html = generateWidgetHTML(config);
      
      setIsLoading(true);
      setError(null);
      
      // Write HTML to iframe
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (doc) {
        doc.open();
        doc.write(html);
        doc.close();
      }
    }
  }, [config, generateWidgetHTML]);

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    setIsLoading(false);
    setError(null);
  }, []);

  // Handle iframe error
  const handleIframeError = useCallback(() => {
    setIsLoading(false);
    setError('Failed to load widget preview');
  }, []);

  // Listen for messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.type === 'WIDGET_READY') {
        setIsLoading(false);
      } else if (event.data.type === 'WIDGET_ERROR') {
        setError(event.data.payload.message);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Refresh preview
  const refreshPreview = useCallback(() => {
    if (iframeRef.current) {
      const iframe = iframeRef.current;
      iframe.src = iframe.src; // Force reload
    }
  }, []);

  // Toggle orientation
  const toggleOrientation = useCallback(() => {
    setOrientation(prev => prev === 'portrait' ? 'landscape' : 'portrait');
  }, []);

  // Scale controls
  const zoomIn = () => setPreviewScale(Math.min(previewScale + 0.1, 2));
  const zoomOut = () => setPreviewScale(Math.max(previewScale - 0.1, 0.25));
  const resetZoom = () => setPreviewScale(1);

  return (
    <Card className={cn('flex flex-col h-full bg-background', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Live Preview
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshPreview}
              disabled={isLoading}
            >
              <RefreshCw className={cn('h-4 w-4', isLoading && 'animate-spin')} />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsInteractive(!isInteractive)}
            >
              {isInteractive ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        
        {/* Device Controls */}
        <div className="flex items-center justify-between pt-2">
          <div className="flex items-center gap-1">
            {Object.entries(devicePresets).map(([key, device]) => {
              const Icon = device.icon;
              return (
                <Button
                  key={key}
                  variant={previewDevice === key ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setPreviewDevice(key as any)}
                  className="h-8 px-2"
                >
                  <Icon className="h-4 w-4" />
                </Button>
              );
            })}
            
            <Separator orientation="vertical" className="h-6 mx-2" />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleOrientation}
              className="h-8 px-2"
              disabled={previewDevice === 'desktop'}
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center gap-1">
            <Button variant="ghost" size="sm" onClick={zoomOut} className="h-8 px-2">
              <ZoomOut className="h-4 w-4" />
            </Button>
            
            <Button variant="ghost" size="sm" onClick={resetZoom} className="h-8 px-2">
              <span className="text-xs font-mono">{Math.round(previewScale * 100)}%</span>
            </Button>
            
            <Button variant="ghost" size="sm" onClick={zoomIn} className="h-8 px-2">
              <ZoomIn className="h-4 w-4" />
            </Button>
            
            <Separator orientation="vertical" className="h-6 mx-2" />
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDeviceFrame(!showDeviceFrame)}
              className="h-8 px-2"
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 p-0">
        <div className="relative h-full bg-gray-50 dark:bg-gray-900 overflow-auto">
          {/* Device Frame */}
          {showDeviceFrame && previewDevice !== 'desktop' && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div 
                className={cn(
                  'relative bg-gray-800 rounded-3xl p-2',
                  previewDevice === 'tablet' && 'rounded-2xl',
                  previewDevice === 'mobile' && 'rounded-3xl'
                )}
                style={{
                  width: deviceWidth * previewScale + 40,
                  height: deviceHeight * previewScale + 40
                }}
              >
                <div className="w-full h-full bg-black rounded-2xl overflow-hidden">
                  <iframe
                    ref={iframeRef}
                    className="w-full h-full border-0"
                    style={{
                      width: deviceWidth,
                      height: deviceHeight,
                      transform: `scale(${previewScale})`,
                      transformOrigin: 'top left'
                    }}
                    onLoad={handleIframeLoad}
                    onError={handleIframeError}
                    sandbox={isInteractive ? 'allow-scripts allow-same-origin' : ''}
                    title="Widget Preview"
                  />
                </div>
              </div>
            </div>
          )}
          
          {/* No Frame */}
          {(!showDeviceFrame || previewDevice === 'desktop') && (
            <div className="flex items-center justify-center h-full p-4">
              <div 
                style={{
                  width: deviceWidth * previewScale,
                  height: deviceHeight * previewScale,
                  maxWidth: '100%',
                  maxHeight: '100%'
                }}
                className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800"
              >
                <iframe
                  ref={iframeRef}
                  className="w-full h-full border-0"
                  style={{
                    width: deviceWidth,
                    height: deviceHeight,
                    transform: `scale(${previewScale})`,
                    transformOrigin: 'top left'
                  }}
                  onLoad={handleIframeLoad}
                  onError={handleIframeError}
                  sandbox={isInteractive ? 'allow-scripts allow-same-origin' : ''}
                  title="Widget Preview"
                />
              </div>
            </div>
          )}
          
          {/* Loading State */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80">
              <div className="flex items-center gap-2 text-muted-foreground">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Loading preview...</span>
              </div>
            </div>
          )}
          
          {/* Error State */}
          {error && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80">
              <div className="text-center text-destructive">
                <p className="font-medium">Preview Error</p>
                <p className="text-sm text-muted-foreground mt-1">{error}</p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={refreshPreview}
                  className="mt-2"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default WidgetPreview;

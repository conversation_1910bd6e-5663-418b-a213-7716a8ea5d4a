import React from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Check,
  MessageSquare,
  Settings,
  Code,
  Zap,
  Shield,
  BarChart,
  Globe,
} from "lucide-react";

const Home = () => {
  return (
    <>
      {/* Hero Section */}
      <section className="container py-24 md:py-32">
        <div className="flex flex-col items-center text-center space-y-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="space-y-4"
          >
            <Badge className="mb-4">Enterprise-Ready AI</Badge>
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
              Transform Customer Support <br />
              <span className="text-primary">With Intelligent AI</span>
            </h1>
            <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
              Deploy a fully customizable AI chat widget powered by leading models like GPT-4, Claude, and Gemini. Reduce support costs by 60% while improving customer satisfaction.
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="flex flex-col sm:flex-row gap-4"
          >
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              Start Free Trial
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
            <Button size="lg" variant="outline">
              Schedule Demo
            </Button>
          </motion.div>
          <p className="text-sm text-muted-foreground">
            No credit card required • 14-day free trial • Cancel anytime
          </p>
        </div>
      </section>

      {/* Trusted By Section */}
      <section className="container pb-24">
        <div className="text-center space-y-4">
          <p className="text-sm text-muted-foreground uppercase tracking-wider">Trusted by innovative companies</p>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-70">
            <div className="h-8">Company 1</div>
            <div className="h-8">Company 2</div>
            <div className="h-8">Company 3</div>
            <div className="h-8">Company 4</div>
            <div className="h-8">Company 5</div>
          </div>
        </div>
      </section>

      {/* Feature Highlights */}
      <section className="container py-24 space-y-16">
        <div className="text-center space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold">Enterprise-Grade AI Support</h2>
          <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
            Everything you need to deliver exceptional AI-powered customer experiences
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8">
          <FeatureCard
            icon={<MessageSquare className="h-10 w-10 text-primary" />}
            title="Multi-AI Integration"
            description="Connect with OpenAI, Anthropic, Google, Mistral, and more. Switch providers instantly with zero downtime."
          />
          <FeatureCard
            icon={<Settings className="h-10 w-10 text-primary" />}
            title="Complete Customization"
            description="Match your brand perfectly with our advanced styling options, custom prompts, and behavior controls."
          />
          <FeatureCard
            icon={<Code className="h-10 w-10 text-primary" />}
            title="Seamless Embedding"
            description="Deploy on any platform with our one-click embed code. Works with websites, mobile apps, and more."
          />
          <FeatureCard
            icon={<Shield className="h-10 w-10 text-primary" />}
            title="Enterprise Security"
            description="SOC 2 compliant with end-to-end encryption, data residency options, and role-based access control."
          />
          <FeatureCard
            icon={<BarChart className="h-10 w-10 text-primary" />}
            title="Advanced Analytics"
            description="Track performance metrics, user satisfaction, and AI response quality with our comprehensive dashboard."
          />
          <FeatureCard
            icon={<Globe className="h-10 w-10 text-primary" />}
            title="Multilingual Support"
            description="Engage with customers in 95+ languages with automatic translation and localization features."
          />
        </div>
      </section>

      {/* How It Works */}
      <section className="bg-muted py-24">
        <div className="container space-y-16">
          <div className="text-center space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold">Deploy in Minutes</h2>
            <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
              Our streamlined setup process gets you up and running quickly
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <StepCard
              number="1"
              title="Connect AI Providers"
              description="Securely link your preferred AI services with our simple API integration."
            />
            <StepCard
              number="2"
              title="Design Your Experience"
              description="Customize every aspect of your widget with our intuitive visual editor."
            />
            <StepCard
              number="3"
              title="Deploy Anywhere"
              description="Copy your unique embed code and paste it into any digital platform."
            />
          </div>
          
          <div className="text-center pt-8">
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              Start Building Now
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Pricing */}
      <section className="container py-24 space-y-16">
        <div className="text-center space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold">
            Transparent, Scalable Pricing
          </h2>
          <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
            Plans that grow with your business needs
          </p>
        </div>

        <Tabs defaultValue="monthly" className="w-full max-w-3xl mx-auto">
          <TabsList className="grid w-full grid-cols-2 mb-8">
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="yearly">Yearly (Save 20%)</TabsTrigger>
          </TabsList>
          <TabsContent value="monthly" className="grid md:grid-cols-2 gap-8">
            <PricingCard
              title="Business"
              price="$99"
              period="/month"
              description="Perfect for growing businesses"
              features={[
                "3 AI Providers",
                "Full Widget Customization",
                "Up to 5,000 messages/month",
                "Basic Analytics",
                "Email & Chat Support",
                "99.9% Uptime SLA"
              ]}
              buttonText="Start 14-Day Trial"
            />
            <PricingCard
              title="Enterprise"
              price="$249"
              period="/month"
              description="For organizations with advanced needs"
              features={[
                "Unlimited AI Providers",
                "Advanced Customization",
                "Unlimited messages",
                "Advanced Analytics & Reporting",
                "Dedicated Account Manager",
                "99.99% Uptime SLA",
                "Custom AI Training"
              ]}
              buttonText="Contact Sales"
              featured={true}
            />
          </TabsContent>
          <TabsContent value="yearly" className="grid md:grid-cols-2 gap-8">
            <PricingCard
              title="Business"
              price="$948"
              period="/year"
              description="Perfect for growing businesses"
              features={[
                "3 AI Providers",
                "Full Widget Customization",
                "Up to 5,000 messages/month",
                "Basic Analytics",
                "Email & Chat Support",
                "99.9% Uptime SLA"
              ]}
              buttonText="Start 14-Day Trial"
            />
            <PricingCard
              title="Enterprise"
              price="$2,388"
              period="/year"
              description="For organizations with advanced needs"
              features={[
                "Unlimited AI Providers",
                "Advanced Customization",
                "Unlimited messages",
                "Advanced Analytics & Reporting",
                "Dedicated Account Manager",
                "99.99% Uptime SLA",
                "Custom AI Training"
              ]}
              buttonText="Contact Sales"
              featured={true}
            />
          </TabsContent>
        </Tabs>
      </section>

      {/* Testimonials */}
      <section className="bg-muted py-24">
        <div className="container space-y-16">
          <div className="text-center space-y-4">
            <h2 className="text-3xl md:text-4xl font-bold">
              Trusted by Industry Leaders
            </h2>
            <p className="text-xl text-muted-foreground max-w-[800px] mx-auto">
              See how our customers are transforming their support experience
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <TestimonialCard
              quote="We reduced our support response time by 78% and increased customer satisfaction scores by 23% within the first month."
              author="Sarah Johnson"
              role="Head of Customer Experience"
              company="TechCorp Inc."
              image="/avatars/avatar-1.png"
            />
            <TestimonialCard
              quote="The multi-AI provider feature gives us incredible flexibility. When one provider has issues, we can switch instantly with zero downtime."
              author="Michael Chen"
              role="CTO"
              company="Quantum Solutions"
              image="/avatars/avatar-2.png"
            />
            <TestimonialCard
              quote="Our support team can now handle 5x the volume of requests without adding headcount. The ROI has been incredible."
              author="Jessica Williams"
              role="Director of Operations"
              company="Global Retail Group"
              image="/avatars/avatar-3.png"
            />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-24">
        <div className="bg-primary text-primary-foreground rounded-xl p-8 md:p-16">
          <div className="max-w-3xl mx-auto text-center space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              Ready to Transform Your Customer Support?
            </h2>
            <p className="text-xl opacity-90">
              Join thousands of companies delivering exceptional AI-powered support experiences.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button size="lg" variant="secondary">
                Start Free Trial
              </Button>
              <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white/10">
                Schedule Demo
              </Button>
            </div>
            <p className="text-sm opacity-75">
              No credit card required • 14-day free trial • Cancel anytime
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard = ({ icon, title, description }: FeatureCardProps) => {
  return (
    <Card>
      <CardContent className="p-6 space-y-4">
        <div className="bg-primary/10 w-14 h-14 rounded-lg flex items-center justify-center">
          {icon}
        </div>
        <h3 className="text-xl font-bold">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
};

interface StepCardProps {
  number: string;
  title: string;
  description: string;
}

const StepCard = ({ number, title, description }: StepCardProps) => {
  return (
    <div className="flex flex-col items-center text-center space-y-4">
      <div className="bg-primary text-primary-foreground w-12 h-12 rounded-full flex items-center justify-center text-xl font-bold">
        {number}
      </div>
      <h3 className="text-xl font-bold">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </div>
  );
};

interface PricingCardProps {
  title: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  buttonText: string;
  featured?: boolean;
}

const PricingCard = ({
  title,
  price,
  period,
  description,
  features,
  buttonText,
  featured = false,
}: PricingCardProps) => {
  return (
    <Card
      className={`relative overflow-hidden ${
        featured
          ? "border-primary shadow-lg shadow-primary/20"
          : "border-border"
      }`}
    >
      {featured && (
        <div className="absolute top-0 right-0 bg-primary text-primary-foreground px-3 py-1 text-xs font-medium">
          Popular
        </div>
      )}
      <CardContent className="p-6 space-y-6">
        <div>
          <h3 className="text-2xl font-bold">{title}</h3>
          <p className="text-muted-foreground mt-1">{description}</p>
        </div>
        <div>
          <span className="text-4xl font-bold">{price}</span>
          <span className="text-muted-foreground">{period}</span>
        </div>
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-2">
              <Check className="h-5 w-5 text-primary" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
        <Button
          className={`w-full ${
            featured ? "bg-primary hover:bg-primary/90" : ""
          }`}
          variant={featured ? "default" : "outline"}
        >
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );
};

interface TestimonialCardProps {
  quote: string;
  author: string;
  role: string;
  company: string;
  image: string;
}

const TestimonialCard = ({
  quote,
  author,
  role,
  company,
  image,
}: TestimonialCardProps) => {
  return (
    <Card>
      <CardContent className="p-6 space-y-4">
        <p className="italic">"{quote}"</p>
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-full bg-muted-foreground/20"></div>
          <div>
            <p className="font-medium">{author}</p>
            <p className="text-sm text-muted-foreground">
              {role}, {company}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Home;

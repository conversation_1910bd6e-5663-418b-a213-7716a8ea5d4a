import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowLeft, MoonIcon, SunIcon, Zap } from "lucide-react";
import { authAPI } from "@/lib/api";

// Form validation schema
const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(8, {
    message: "Password must be at least 8 characters",
  }),
  rememberMe: z.boolean().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface LoginProps {
  isDarkMode: boolean;
  toggleTheme: () => void;
}

const Login: React.FC<LoginProps> = ({ isDarkMode, toggleTheme }) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isQuickLoginLoading, setIsQuickLoginLoading] = useState(false);

  // Initialize form with validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    try {
      const response = await authAPI.login({
        email: data.email,
        password: data.password,
        remember_me: data.rememberMe,
      });

      toast({
        title: "Login successful!",
        description: `Welcome back, ${response.user.name}!`,
      });

      // Redirect based on user role
      if (response.user.role === "admin") {
        navigate("/admin/dashboard");
      } else {
        navigate("/");
      }
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Login failed",
        description:
          error.message || "Please check your credentials and try again.",
      });
    }
  };

  // Handle quick admin login
  const handleQuickAdminLogin = async () => {
    setIsQuickLoginLoading(true);
    try {
      const response = await authAPI.quickAdminLogin();

      toast({
        title: "Quick Admin Login Successful!",
        description: `Welcome, ${response.user.name}! You now have full admin access.`,
      });

      // Redirect to admin dashboard
      navigate("/admin/dashboard");
    } catch (error: any) {
      toast({
        variant: "destructive",
        title: "Quick Admin Login Failed",
        description: error.message || "Unable to perform quick admin login.",
      });
    } finally {
      setIsQuickLoginLoading(false);
    }
  };

  return (
    <div className="flex min-h-[calc(100vh-4rem)]">
      {/* Left Column - Form */}
      <div className="w-full lg:w-1/2 p-8 flex flex-col">
        <div className="flex justify-between items-center mb-8">
          <Link
            to="/"
            className="flex items-center text-sm text-muted-foreground hover:text-primary transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to home
          </Link>
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="rounded-full"
            aria-label="Toggle theme"
          >
            {isDarkMode ? (
              <SunIcon className="h-5 w-5" />
            ) : (
              <MoonIcon className="h-5 w-5" />
            )}
          </Button>
        </div>

        <div className="max-w-md mx-auto w-full flex-1 flex flex-col justify-center">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold mb-2">Welcome back</h1>
            <p className="text-muted-foreground">
              Enter your credentials to access your account
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        type="email"
                        autoComplete="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <div className="flex items-center justify-between">
                      <FormLabel>Password</FormLabel>
                      <Link
                        to="/forgot-password"
                        className="text-xs text-primary hover:underline"
                      >
                        Forgot password?
                      </Link>
                    </div>
                    <FormControl>
                      <Input
                        placeholder="••••••••"
                        type="password"
                        autoComplete="current-password"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="text-sm font-normal">
                        Remember me for 30 days
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? "Signing in..." : "Sign in"}
              </Button>
            </form>
          </Form>

          {/* Quick Admin Login Button */}
          <div className="mt-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Demo Access
                </span>
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              className="w-full mt-4 border-2 border-dashed border-primary/50 hover:border-primary hover:bg-primary/5 transition-all duration-200"
              onClick={handleQuickAdminLogin}
              disabled={isQuickLoginLoading}
            >
              <Zap className="mr-2 h-4 w-4" />
              {isQuickLoginLoading ? "Logging in..." : "Quick Admin Login"}
            </Button>
            <p className="text-xs text-muted-foreground text-center mt-2">
              Instant access with full admin permissions for demo/testing
            </p>
          </div>

          <div className="mt-6 text-center text-sm">
            Don't have an account?{" "}
            <Link
              to="/signup"
              className="text-primary font-medium hover:underline"
            >
              Create an account
            </Link>
          </div>
        </div>
      </div>

      {/* Right Column - Image/Branding */}
      <div className="hidden lg:block lg:w-1/2 bg-gradient-to-br from-primary/90 to-primary/70 text-primary-foreground">
        <div className="h-full flex flex-col justify-center items-center p-12">
          <div className="w-full max-w-md">
            <div className="mb-8">
              <div className="w-12 h-12 bg-primary-foreground rounded-full flex items-center justify-center mb-4">
                <span className="text-primary font-bold text-xl">AI</span>
              </div>
              <h2 className="text-3xl font-bold mb-4">ChatSupport AI</h2>
              <p className="text-primary-foreground/80 text-lg mb-8">
                Enterprise-grade AI chat system designed for multi-platform
                customer support.
              </p>
            </div>

            <div className="bg-primary-foreground/10 backdrop-blur-sm rounded-lg p-6 border border-primary-foreground/20">
              <p className="italic text-primary-foreground/90 mb-4">
                "ChatSupport has transformed our customer service experience.
                Response times are down 78% and customer satisfaction is up
                23%."
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-primary-foreground/20 mr-3"></div>
                <div>
                  <p className="font-medium">Sarah Johnson</p>
                  <p className="text-sm text-primary-foreground/70">
                    Head of Customer Experience, TechCorp
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;

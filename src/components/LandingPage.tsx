import React, { useEffect } from "react";
import { motion, useAnimation, useScroll } from "framer-motion";
import { useInView } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowRight,
  Check,
  MessageSquare,
  Shield,
  Zap,
  Globe,
  BarChart,
  Code,
  ChevronDown,
} from "lucide-react";

const LandingPage = () => {
  const { scrollYProgress } = useScroll();

  return (
    <>
      {/* Hero Section - Full Screen with Parallax */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-primary/5">
        {/* Background Elements */}
        <div className="absolute inset-0 z-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-primary/10 rounded-full blur-3xl" />
          <div className="absolute bottom-10 right-10 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-grid-pattern opacity-[0.03]" />
        </div>

        {/* Content */}
        <div className="container relative z-10 px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.8 }}
            >
              <Badge className="px-4 py-1.5 text-sm font-medium mb-6 bg-primary/10 text-primary border-primary/20">
                ENTERPRISE AI SOLUTION
              </Badge>
            </motion.div>
            
            <motion.h1
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4, duration: 0.8 }}
              className="text-5xl md:text-7xl font-bold tracking-tight mb-6 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/70"
            >
              Redefine Customer <br />
              <span className="text-primary">Support with AI</span>
            </motion.h1>
            
            <motion.p
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="text-xl md:text-2xl text-muted-foreground mb-10 max-w-3xl mx-auto"
            >
              Deploy enterprise-grade AI chat widgets powered by leading models. 
              Cut support costs by 60% while delivering exceptional experiences.
            </motion.p>
            
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-lg h-14 px-8">
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="text-lg h-14 px-8">
                Watch Demo
              </Button>
            </motion.div>
            
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 0.8 }}
              className="text-sm text-muted-foreground mt-6"
            >
              No credit card required • 14-day free trial • Enterprise-ready
            </motion.p>
          </motion.div>
        </div>

        {/* Scroll Indicator */}
        <motion.div 
          initial={{ opacity: 0 }}
          animate={{ opacity: 1, y: [0, 10, 0] }}
          transition={{ delay: 1.5, duration: 1.5, repeat: Infinity }}
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
        >
          <ChevronDown className="h-8 w-8 text-primary/50" />
        </motion.div>
      </section>

      {/* Brands Section */}
      <section className="py-20 bg-muted/30">
        <div className="container px-4">
          <FadeInWhenVisible>
            <div className="text-center mb-12">
              <p className="text-sm uppercase tracking-widest text-muted-foreground font-medium mb-4">
                Trusted by industry leaders
              </p>
              <div className="flex flex-wrap justify-center items-center gap-x-16 gap-y-8">
                {['Brand 1', 'Brand 2', 'Brand 3', 'Brand 4', 'Brand 5'].map((brand, index) => (
                  <div 
                    key={index} 
                    className="h-8 w-32 bg-muted/30 rounded flex items-center justify-center text-muted-foreground/70 font-medium"
                  >
                    {brand}
                  </div>
                ))}
              </div>
            </div>
          </FadeInWhenVisible>
        </div>
      </section>

      {/* Features Section - Full Screen with Grid */}
      <section className="min-h-screen py-32 relative overflow-hidden">
        <div className="container px-4 relative z-10">
          <FadeInWhenVisible>
            <div className="text-center max-w-3xl mx-auto mb-20">
              <Badge className="px-3 py-1 text-sm mb-6 bg-primary/10 text-primary border-primary/20">
                POWERFUL FEATURES
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Enterprise-Grade AI Support
              </h2>
              <p className="text-xl text-muted-foreground">
                Everything you need to deliver exceptional AI-powered customer experiences
              </p>
            </div>
          </FadeInWhenVisible>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<MessageSquare className="h-8 w-8" />}
              title="Multi-AI Integration"
              description="Connect with OpenAI, Anthropic, Google, Mistral, and more. Switch providers instantly with zero downtime."
              delay={0.1}
            />
            <FeatureCard
              icon={<Code className="h-8 w-8" />}
              title="Seamless Embedding"
              description="Deploy on any platform with our one-click embed code. Works with websites, mobile apps, and more."
              delay={0.2}
            />
            <FeatureCard
              icon={<Shield className="h-8 w-8" />}
              title="Enterprise Security"
              description="SOC 2 compliant with end-to-end encryption, data residency options, and role-based access control."
              delay={0.3}
            />
            <FeatureCard
              icon={<BarChart className="h-8 w-8" />}
              title="Advanced Analytics"
              description="Track performance metrics, user satisfaction, and AI response quality with our comprehensive dashboard."
              delay={0.4}
            />
            <FeatureCard
              icon={<Globe className="h-8 w-8" />}
              title="Multilingual Support"
              description="Engage with customers in 95+ languages with automatic translation and localization features."
              delay={0.5}
            />
            <FeatureCard
              icon={<Zap className="h-8 w-8" />}
              title="Complete Customization"
              description="Match your brand perfectly with our advanced styling options, custom prompts, and behavior controls."
              delay={0.6}
            />
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="absolute top-1/4 right-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 left-0 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
        </div>
      </section>

      {/* How It Works - Full Screen with Steps */}
      <section className="min-h-screen py-32 bg-gradient-to-b from-background to-muted/30 relative overflow-hidden">
        <div className="container px-4 relative z-10">
          <FadeInWhenVisible>
            <div className="text-center max-w-3xl mx-auto mb-20">
              <Badge className="px-3 py-1 text-sm mb-6 bg-primary/10 text-primary border-primary/20">
                SIMPLE PROCESS
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Deploy in Minutes
              </h2>
              <p className="text-xl text-muted-foreground">
                Our streamlined setup process gets you up and running quickly
              </p>
            </div>
          </FadeInWhenVisible>

          <div className="relative">
            {/* Connection Line */}
            <div className="hidden md:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-primary/0 via-primary/30 to-primary/0 transform -translate-y-1/2 z-0" />
            
            <div className="grid md:grid-cols-3 gap-12 md:gap-8 relative z-10">
              <StepCard
                number="01"
                title="Connect AI Providers"
                description="Securely link your preferred AI services with our simple API integration."
                delay={0.1}
              />
              <StepCard
                number="02"
                title="Design Your Experience"
                description="Customize every aspect of your widget with our intuitive visual editor."
                delay={0.3}
              />
              <StepCard
                number="03"
                title="Deploy Anywhere"
                description="Copy your unique embed code and paste it into any digital platform."
                delay={0.5}
              />
            </div>
          </div>
          
          <FadeInWhenVisible>
            <div className="text-center mt-20">
              <Button size="lg" className="bg-primary hover:bg-primary/90 text-lg h-14 px-8">
                Start Building Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </FadeInWhenVisible>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="absolute top-1/3 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
        </div>
      </section>

      {/* Testimonials Section - Full Screen with Cards */}
      <section className="min-h-screen py-32 relative overflow-hidden">
        <div className="container px-4 relative z-10">
          <FadeInWhenVisible>
            <div className="text-center max-w-3xl mx-auto mb-20">
              <Badge className="px-3 py-1 text-sm mb-6 bg-primary/10 text-primary border-primary/20">
                SUCCESS STORIES
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Trusted by Industry Leaders
              </h2>
              <p className="text-xl text-muted-foreground">
                See how our customers are transforming their support experience
              </p>
            </div>
          </FadeInWhenVisible>

          <div className="grid md:grid-cols-3 gap-8">
            <TestimonialCard
              quote="We reduced our support response time by 78% and increased customer satisfaction scores by 23% within the first month."
              author="Sarah Johnson"
              role="Head of Customer Experience"
              company="TechCorp Inc."
              delay={0.1}
            />
            <TestimonialCard
              quote="The multi-AI provider feature gives us incredible flexibility. When one provider has issues, we can switch instantly with zero downtime."
              author="Michael Chen"
              role="CTO"
              company="Quantum Solutions"
              delay={0.3}
            />
            <TestimonialCard
              quote="Our support team can now handle 5x the volume of requests without adding headcount. The ROI has been incredible."
              author="Jessica Williams"
              role="Director of Operations"
              company="Global Retail Group"
              delay={0.5}
            />
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="absolute top-1/4 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 right-0 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
        </div>
      </section>

      {/* Pricing Section - Full Screen with Cards */}
      <section className="min-h-screen py-32 bg-gradient-to-b from-background to-muted/30 relative overflow-hidden">
        <div className="container px-4 relative z-10">
          <FadeInWhenVisible>
            <div className="text-center max-w-3xl mx-auto mb-20">
              <Badge className="px-3 py-1 text-sm mb-6 bg-primary/10 text-primary border-primary/20">
                PRICING
              </Badge>
              <h2 className="text-4xl md:text-5xl font-bold mb-6">
                Transparent, Scalable Pricing
              </h2>
              <p className="text-xl text-muted-foreground">
                Plans that grow with your business needs
              </p>
            </div>
          </FadeInWhenVisible>

          <div className="flex justify-center mb-12">
            <div className="bg-muted/50 p-1 rounded-full inline-flex">
              <button className="px-6 py-2 rounded-full bg-primary text-primary-foreground">
                Monthly
              </button>
              <button className="px-6 py-2 rounded-full">
                Yearly (Save 20%)
              </button>
            </div>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <PricingCard
              title="Business"
              price="$99"
              period="/month"
              description="Perfect for growing businesses"
              features={[
                "3 AI Providers",
                "Full Widget Customization",
                "Up to 5,000 messages/month",
                "Basic Analytics",
                "Email & Chat Support",
                "99.9% Uptime SLA"
              ]}
              buttonText="Start 14-Day Trial"
              delay={0.1}
            />
            <PricingCard
              title="Enterprise"
              price="$249"
              period="/month"
              description="For organizations with advanced needs"
              features={[
                "Unlimited AI Providers",
                "Advanced Customization",
                "Unlimited messages",
                "Advanced Analytics & Reporting",
                "Dedicated Account Manager",
                "99.99% Uptime SLA",
                "Custom AI Training"
              ]}
              buttonText="Contact Sales"
              featured={true}
              delay={0.3}
            />
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 left-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
        </div>
      </section>

      {/* CTA Section - Full Screen */}
      <section className="min-h-[80vh] py-32 flex items-center justify-center relative overflow-hidden">
        <div className="container px-4 relative z-10">
          <FadeInWhenVisible>
            <div className="bg-gradient-to-br from-primary to-primary/80 rounded-3xl p-12 md:p-20 text-primary-foreground shadow-xl shadow-primary/20">
              <div className="max-w-3xl mx-auto text-center space-y-8">
                <h2 className="text-4xl md:text-6xl font-bold">
                  Ready to Transform Your Customer Support?
                </h2>
                <p className="text-xl md:text-2xl opacity-90">
                  Join thousands of companies delivering exceptional AI-powered support experiences.
                </p>
                <div className="flex flex-col sm:flex-row justify-center gap-4 pt-4">
                  <Button size="lg" variant="secondary" className="text-lg h-14 px-8">
                    Start Free Trial
                  </Button>
                  <Button 
                    size="lg" 
                    variant="outline" 
                    className="bg-transparent border-white text-white hover:bg-white/10 text-lg h-14 px-8"
                  >
                    Schedule Demo
                  </Button>
                </div>
                <p className="text-sm opacity-75">
                  No credit card required • 14-day free trial • Cancel anytime
                </p>
              </div>
            </div>
          </FadeInWhenVisible>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="absolute inset-0 bg-grid-pattern opacity-[0.03]" />
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
        </div>
      </section>

      {/* Modern Footer */}
      <footer className="relative overflow-hidden bg-gradient-to-b from-background to-muted/30 pt-20 pb-10">
        {/* Background Elements */}
        <div className="absolute inset-0 z-0 pointer-events-none">
          <div className="absolute bottom-0 left-0 w-full h-[1px] bg-gradient-to-r from-transparent via-muted/50 to-transparent" />
          <div className="absolute top-1/4 right-10 w-64 h-64 bg-primary/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/4 left-10 w-80 h-80 bg-primary/5 rounded-full blur-3xl" />
        </div>

        <div className="container px-4 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12 mb-16">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.8 }}
              >
                <div className="flex items-center space-x-2 mb-6">
                  <div className="w-10 h-10 bg-primary rounded-md flex items-center justify-center">
                    <span className="text-primary-foreground font-bold text-lg">AI</span>
                  </div>
                  <span className="font-bold text-2xl">ChatSupport</span>
                </div>
                <p className="text-muted-foreground mb-6 max-w-md">
                  Enterprise-grade, embeddable AI chat system designed for
                  multi-platform customer support. Reduce costs and improve
                  customer satisfaction with our cutting-edge solution.
                </p>
                <div className="flex space-x-4">
                  <a 
                    href="#twitter"
                    className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center hover:bg-primary/20 transition-colors"
                  >
                    <span className="sr-only">Twitter</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="opacity-70">
                      <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                    </svg>
                  </a>
                  <a 
                    href="#linkedin"
                    className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center hover:bg-primary/20 transition-colors"
                  >
                    <span className="sr-only">LinkedIn</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="opacity-70">
                      <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                      <rect width="4" height="12" x="2" y="9"></rect>
                      <circle cx="4" cy="4" r="2"></circle>
                    </svg>
                  </a>
                  <a 
                    href="#github"
                    className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center hover:bg-primary/20 transition-colors"
                  >
                    <span className="sr-only">GitHub</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="opacity-70">
                      <path d="M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"></path>
                      <path d="M9 18c-4.51 2-5-2-7-2"></path>
                    </svg>
                  </a>
                  <a 
                    href="#facebook"
                    className="w-10 h-10 rounded-full bg-muted/50 flex items-center justify-center hover:bg-primary/20 transition-colors"
                  >
                    <span className="sr-only">Facebook</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="opacity-70">
                      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                    </svg>
                  </a>
                </div>
              </motion.div>
            </div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.1 }}
            >
              <h3 className="font-semibold text-lg mb-6">Product</h3>
              <ul className="space-y-4">
                {['Features', 'Pricing', 'Integrations', 'Documentation', 'API Reference'].map((link) => (
                  <li key={link}>
                    <a href={`#${link.toLowerCase()}`} className="text-muted-foreground hover:text-primary transition-colors">
                      {link}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Company */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <h3 className="font-semibold text-lg mb-6">Company</h3>
              <ul className="space-y-4">
                {['About', 'Blog', 'Careers', 'Contact', 'Partners'].map((link) => (
                  <li key={link}>
                    <a href={`#${link.toLowerCase()}`} className="text-muted-foreground hover:text-primary transition-colors">
                      {link}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Newsletter */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <h3 className="font-semibold text-lg mb-6">Stay Updated</h3>
              <p className="text-muted-foreground mb-4">
                Subscribe to our newsletter for the latest updates and insights.
              </p>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Your email"
                  className="bg-muted/50 border border-muted rounded-l-md px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <button className="bg-primary text-primary-foreground px-4 rounded-r-md hover:bg-primary/90 transition-colors">
                  →
                </button>
              </div>
            </motion.div>
          </div>

          {/* Bottom Bar */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="pt-8 mt-8 border-t border-muted/30 flex flex-col md:flex-row justify-between items-center"
          >
            <p className="text-sm text-muted-foreground mb-4 md:mb-0">
              © {new Date().getFullYear()} ChatSupport AI. All rights reserved.
            </p>
            <div className="flex flex-wrap justify-center gap-6">
              <a href="#privacy" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                Privacy Policy
              </a>
              <a href="#terms" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                Terms of Service
              </a>
              <a href="#cookies" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                Cookie Policy
              </a>
              <a href="#sitemap" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                Sitemap
              </a>
            </div>
          </motion.div>
        </div>
      </footer>
    </>
  );
};

// Animation component for fade-in when element comes into view
const FadeInWhenVisible = ({ children }) => {
  const controls = useAnimation();
  const ref = React.useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });
  
  useEffect(() => {
    if (isInView) {
      controls.start("visible");
    }
  }, [controls, isInView]);
  
  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 20 },
        visible: { opacity: 1, y: 0, transition: { duration: 0.8 } }
      }}
    >
      {children}
    </motion.div>
  );
};

// Feature Card Component
const FeatureCard = ({ icon, title, description, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ delay, duration: 0.8 }}
    >
      <div className="bg-white/5 backdrop-blur-sm border border-muted rounded-xl p-8 h-full hover:border-primary/20 hover:shadow-lg hover:shadow-primary/5 transition-all duration-300">
        <div className="bg-primary/10 text-primary w-16 h-16 rounded-lg flex items-center justify-center mb-6">
          {icon}
        </div>
        <h3 className="text-2xl font-bold mb-4">{title}</h3>
        <p className="text-muted-foreground">{description}</p>
      </div>
    </motion.div>
  );
};

// Step Card Component
const StepCard = ({ number, title, description, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ delay, duration: 0.8 }}
      className="flex flex-col items-center text-center"
    >
      <div className="bg-primary text-primary-foreground w-16 h-16 rounded-full flex items-center justify-center text-xl font-bold mb-6">
        {number}
      </div>
      <h3 className="text-2xl font-bold mb-4">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
    </motion.div>
  );
};

// Testimonial Card Component
const TestimonialCard = ({ quote, author, role, company, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ delay, duration: 0.8 }}
    >
      <div className="bg-white/5 backdrop-blur-sm border border-muted rounded-xl p-8 h-full hover:border-primary/20 hover:shadow-lg hover:shadow-primary/5 transition-all duration-300">
        <div className="mb-6">
          {[...Array(5)].map((_, i) => (
            <span key={i} className="text-primary">★</span>
          ))}
        </div>
        <p className="text-lg mb-8 italic">"{quote}"</p>
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/30 to-primary/10 flex items-center justify-center text-lg font-bold text-primary">
            {author.charAt(0)}
          </div>
          <div>
            <p className="font-semibold">{author}</p>
            <p className="text-sm text-muted-foreground">
              {role}, {company}
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Pricing Card Component
const PricingCard = ({ title, price, period, description, features, buttonText, featured = false, delay = 0 }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ delay, duration: 0.8 }}
    >
      <div 
        className={`relative overflow-hidden rounded-xl p-8 h-full transition-all duration-300 ${
          featured 
            ? "bg-gradient-to-br from-primary/10 to-transparent border-primary shadow-lg shadow-primary/10" 
            : "bg-white/5 backdrop-blur-sm border border-muted hover:border-primary/20"
        }`}
      >
        {featured && (
          <div className="absolute top-0 right-0 bg-primary text-primary-foreground px-4 py-1 text-sm font-medium rounded-bl-lg">
            Popular
          </div>
        )}
        <div className="mb-8">
          <h3 className="text-2xl font-bold mb-2">{title}</h3>
          <p className="text-muted-foreground mb-6">{description}</p>
          <div className="flex items-baseline mb-2">
            <span className="text-5xl font-bold">{price}</span>
            <span className="text-muted-foreground ml-1">{period}</span>
          </div>
        </div>
        <ul className="space-y-4 mb-8">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-3">
              <div className="bg-primary/10 rounded-full p-1">
                <Check className="h-4 w-4 text-primary" />
              </div>
              <span>{feature}</span>
            </li>
          ))}
        </ul>
        <Button
          className={`w-full h-12 ${
            featured ? "bg-primary hover:bg-primary/90" : ""
          }`}
          variant={featured ? "default" : "outline"}
        >
          {buttonText}
        </Button>
      </div>
    </motion.div>
  );
};

export default LandingPage; 
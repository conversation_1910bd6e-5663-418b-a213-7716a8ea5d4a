import React, { useState, useEffect, useCallback } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import WidgetPreview from "@/components/WidgetPreview";
import {
  Copy,
  Code,
  Eye,
  Save,
  Upload,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Settings,
  Palette,
  Zap,
  Globe,
  ArrowLeft,
  Trash2,
  Share,
  Play,
  Pause,
} from "lucide-react";
import {
  useCurrentConfig,
  useWidgetActions,
  useIsDirty,
  useIsLoading,
  useError,
  useSelectedWidgetId,
} from "@/lib/widget-config-store";
import { WidgetConfig } from "@/types/widget-config";
import { widgetAPI, aiProviderAPI } from "@/lib/api";

interface WidgetConfiguratorProps {
  widgetId?: string;
  onSave?: (config: WidgetConfig) => void;
  onPublish?: (config: WidgetConfig) => void;
}

interface AIModel {
  id: string;
  name: string;
  provider: string;
  description?: string;
  max_tokens: number;
  supports_streaming: boolean;
  is_available: boolean;
}

interface AIProvider {
  id: string;
  name: string;
  slug: string;
  is_configured: boolean;
  models: AIModel[];
  status: "active" | "inactive" | "error";
}

interface ColorPickerProps {
  color?: string;
  onChange?: (color: string) => void;
}

const ColorPicker: React.FC<ColorPickerProps> = ({
  color = "#000000",
  onChange = () => {},
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [tempColor, setTempColor] = useState(color);

  const handleColorChange = (newColor: string) => {
    // Validate hex color format
    const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    if (hexRegex.test(newColor)) {
      onChange(newColor);
      setTempColor(newColor);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <div
        className="w-8 h-8 rounded-md border cursor-pointer transition-all hover:scale-105"
        style={{ backgroundColor: color }}
        onClick={() => setIsOpen(!isOpen)}
      />
      <Input
        type="text"
        value={color}
        onChange={(e) => {
          setTempColor(e.target.value);
          handleColorChange(e.target.value);
        }}
        onBlur={() => {
          if (tempColor !== color) {
            handleColorChange(tempColor);
          }
        }}
        className="w-28 font-mono text-sm"
        placeholder="#000000"
      />
      <input
        type="color"
        value={color}
        onChange={(e) => handleColorChange(e.target.value)}
        className="w-8 h-8 rounded border cursor-pointer"
        style={{
          opacity: 0,
          position: "absolute",
          pointerEvents: isOpen ? "auto" : "none",
        }}
      />
    </div>
  );
};

const WidgetConfigurator: React.FC<WidgetConfiguratorProps> = ({
  widgetId: propWidgetId,
  onSave = () => {},
  onPublish = () => {},
}) => {
  const { id: paramWidgetId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const widgetId = propWidgetId || paramWidgetId;

  const config = useCurrentConfig();
  const isDirty = useIsDirty();
  const isLoading = useIsLoading();
  const error = useError();
  const selectedWidgetId = useSelectedWidgetId();
  const { toast } = useToast();

  const {
    loadWidget,
    createWidget,
    saveCurrentConfig,
    publishWidget,
    unpublishWidget,
    deleteWidget,
    duplicateWidget,
    exportConfig,
    importConfig,
    updateConfig,
    setSelectedWidget,
    resetConfig,
    validateCurrentConfig,
  } = useWidgetActions();

  const [activeTab, setActiveTab] = useState("basic");
  const [codeType, setCodeType] = useState<
    "iframe" | "script" | "react" | "vue"
  >("iframe");
  const [availableProviders, setAvailableProviders] = useState<AIProvider[]>(
    [],
  );
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]);
  const [embedCode, setEmbedCode] = useState<string>("");
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Load widget if ID is provided
  useEffect(() => {
    if (widgetId && widgetId !== selectedWidgetId) {
      loadWidget(widgetId).catch(console.error);
    } else if (!widgetId) {
      createWidget({
        basic: {
          name: "New Widget",
          description: "New Widget",
          isActive: true,
          aiProvider: "openai",
          aiModel: "gpt-4o",
          responseTimeout: 10000,
        },
        styling: {
          backgroundColor: "#ffffff",
          textColor: "#000000",
          borderRadius: 10,
          primaryColor: "#000000",
          secondaryColor: "#000000",
          accentColor: "#000000",
          errorColor: "#000000",
          fontFamily: "Inter",
          customCSS: "",
        },
        positioning: {
          width: 300,
          height: 400,
          position: "bottom-right",
        },
        testing: {
          enableTesting: false,
          testScenarios: [],
          performanceMonitoring: true,
          errorTracking: true,
          loadTesting: false,
          maxConcurrentUsers: 100,
        },
      });
    }
  }, [widgetId, selectedWidgetId, loadWidget, createWidget]);

  // Load AI providers and models on mount
  useEffect(() => {
    loadAIProviders();
  }, []);

  // Load models when provider changes
  useEffect(() => {
    if (config.basic.aiProvider) {
      loadModelsForProvider(config.basic.aiProvider);
    }
  }, [config.basic.aiProvider]);

  // Generate embed code when config changes
  useEffect(() => {
    if (config.id && config.isPublished) {
      generateEmbedCode();
    }
  }, [config, codeType]);

  // Validate config on changes
  useEffect(() => {
    const validation = validateCurrentConfig();
    setValidationErrors(validation.errors);
  }, [config, validateCurrentConfig]);

  const loadAIProviders = async () => {
    try {
      const providers = await aiProviderAPI.getProviders();
      setAvailableProviders(
        providers.filter((p) => p.is_configured && p.status === "active"),
      );
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load AI providers",
        variant: "destructive",
      });
    }
  };

  const loadModelsForProvider = async (providerId: string) => {
    try {
      const models = await aiProviderAPI.getAvailableModels(providerId);
      setAvailableModels(models.filter((m) => m.is_available));
    } catch (error) {
      console.error("Failed to load models:", error);
      setAvailableModels([]);
    }
  };

  const generateEmbedCode = async () => {
    if (!config.id || !config.isPublished) return;

    setIsGeneratingCode(true);
    try {
      const embedOptions = {
        type: codeType,
        widget_id: config.id,
        base_url: window.location.origin,
        width: config.positioning.width,
        height: config.positioning.height,
        custom_css: config.styling.customCSS,
      };
      const code = await widgetAPI.generateEmbedCode(config.id, embedOptions);
      setEmbedCode(code);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate embed code",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingCode(false);
    }
  };

  const updateConfigSection = (
    section: keyof WidgetConfig,
    key: string,
    value: any,
  ) => {
    const updates = {
      [section]: {
        ...(config[section] as Record<string, any>),
        [key]: value,
      },
    };
    updateConfig(updates);
    setSelectedWidget(config.id);
    resetConfig();
    validateCurrentConfig();
  };

  const handleSave = async () => {
    try {
      await saveCurrentConfig();
      onSave(config);
      toast({
        title: "Success",
        description: "Widget configuration saved successfully",
      });

      // Navigate to the widget edit page if it's a new widget
      if (!widgetId && config.id) {
        navigate(`/admin/widgets/${config.id}`);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save widget configuration",
        variant: "destructive",
      });
    }
  };

  const handleCreateNew = async () => {
    try {
      const newWidget = await createWidget({
        ...config,
        basic: {
          ...config.basic,
          name: config.basic.name || "New Widget",
        },
      });
      onSave(newWidget);
      toast({
        title: "Success",
        description: "New widget created successfully",
      });
      navigate(`/admin/widgets/${newWidget.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create widget",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async () => {
    if (!config.id) return;

    try {
      await deleteWidget(config.id);
      toast({
        title: "Success",
        description: "Widget deleted successfully",
      });
      navigate("/admin/widgets");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete widget",
        variant: "destructive",
      });
    }
  };

  const handleDuplicate = async () => {
    if (!config.id) return;

    try {
      const duplicatedWidget = await duplicateWidget(config.id);
      toast({
        title: "Success",
        description: "Widget duplicated successfully",
      });
      navigate(`/admin/widgets/${duplicatedWidget.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate widget",
        variant: "destructive",
      });
    }
  };

  const handleUnpublish = async () => {
    if (!config.id) return;

    try {
      const unpublishedWidget = await unpublishWidget(config.id);
      onPublish(unpublishedWidget);
      toast({
        title: "Success",
        description: "Widget unpublished successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unpublish widget",
        variant: "destructive",
      });
    }
  };

  const handlePublish = async () => {
    if (!config.id) {
      toast({
        title: "Error",
        description: "Please save the widget before publishing",
        variant: "destructive",
      });
      return;
    }

    const validation = validateCurrentConfig();
    if (!validation.isValid) {
      toast({
        title: "Validation Error",
        description: "Please fix validation errors before publishing",
        variant: "destructive",
      });
      return;
    }

    try {
      const publishedWidget = await publishWidget(config.id);
      onPublish(publishedWidget);
      toast({
        title: "Success",
        description: "Widget published successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to publish widget",
        variant: "destructive",
      });
    }
    resetConfig();
    validateCurrentConfig();
  };

  const handleExport = async () => {
    try {
      const blob = await exportConfig("json");
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${config.basic.name || "widget"}-config.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Success",
        description: "Widget configuration exported",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to export configuration",
        variant: "destructive",
      });
    }
  };

  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      await importConfig(file);
      toast({
        title: "Success",
        description: "Widget configuration imported",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to import configuration",
        variant: "destructive",
      });
    }

    // Reset file input
    event.target.value = "";
  };

  const copyEmbedCode = async () => {
    try {
      await navigator.clipboard.writeText(embedCode);
      toast({
        title: "Success",
        description: "Embed code copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy embed code",
        variant: "destructive",
      });
    }
  };

  const getEmbedCode = () => {
    const baseUrl = window.location.origin;
    const widgetId = config.id;

    if (!widgetId || !config.isPublished) {
      return "// Please save and publish the widget first";
    }

    // Return the generated embed code from API if available
    if (embedCode) {
      return embedCode;
    }

    // Fallback to client-side generation if API code is not available
    switch (codeType) {
      case "iframe":
        return `<iframe 
  src="${baseUrl}/api/v1/widgets/${widgetId}/embed" 
  width="${config.positioning.width}" 
  height="${config.positioning.height}" 
  frameborder="0"
  style="border: none; border-radius: ${config.styling.borderRadius}px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);"
  allow="microphone; camera"
  sandbox="allow-scripts allow-same-origin allow-forms"
></iframe>`;

      case "script":
        return `<script>
  (function(w,d,s,o,f,js,fjs){
    w['ChatSupportWidget']=o;w[o]=w[o]||function(){(w[o].q=w[o].q||[]).push(arguments)};
    js=d.createElement(s),fjs=d.getElementsByTagName(s)[0];
    js.id=o;js.src=f;js.async=1;fjs.parentNode.insertBefore(js,fjs);
  }(window,document,'script','csw','${baseUrl}/js/widget.js'));
  csw('init', {
    widgetId: '${widgetId}',
    apiUrl: '${baseUrl}/api/v1',
    position: '${config.positioning.position}',
    primaryColor: '${config.styling.primaryColor}',
    autoOpen: ${config.behavior.autoOpen}
  });
</script>`;

      case "react":
        return `import React from 'react';
import { ChatSupportWidget } from '@your-company/chat-widget-react';

function App() {
  return (
    <div>
      {/* Your app content */}
      <ChatSupportWidget 
        widgetId="${widgetId}"
        apiUrl="${baseUrl}/api/v1"
        config={{
          position: '${config.positioning.position}',
          primaryColor: '${config.styling.primaryColor}',
          autoOpen: ${config.behavior.autoOpen},
          welcomeMessage: '${config.behavior.welcomeMessage}'
        }}
      />
    </div>
  );
}

export default App;`;

      case "vue":
        return `<template>
  <div>
    <!-- Your app content -->
    <ChatSupportWidget 
      :widget-id="'${widgetId}'"
      :api-url="'${baseUrl}/api/v1'"
      :config="widgetConfig"
    />
  </div>
</template>

<script>
import { ChatSupportWidget } from '@your-company/chat-widget-vue';

export default {
  components: {
    ChatSupportWidget
  },
  data() {
    return {
      widgetConfig: {
        position: '${config.positioning.position}',
        primaryColor: '${config.styling.primaryColor}',
        autoOpen: ${config.behavior.autoOpen},
        welcomeMessage: '${config.behavior.welcomeMessage}'
      }
    };
  }
};
</script>`;

      case "wordpress":
        return `<!-- Add this shortcode to any page or post -->
[chat_support_widget id="${widgetId}"]

<!-- Or use the PHP function in your theme -->
<?php
if (function_exists('render_chat_support_widget')) {
    render_chat_support_widget('${widgetId}', array(
        'position' => '${config.positioning.position}',
        'primary_color' => '${config.styling.primaryColor}',
        'auto_open' => ${config.behavior.autoOpen ? "true" : "false"}
    ));
}
?>`;

      default:
        return "// Embed code will appear here";
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full bg-background">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-16 items-center justify-between px-6">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/admin/widgets")}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Widgets
            </Button>
            <div>
              <h1 className="text-xl font-semibold">
                {config.id
                  ? `Edit Widget: ${config.basic.name}`
                  : "Create New Widget"}
              </h1>
              <p className="text-sm text-muted-foreground">
                {config.id
                  ? `ID: ${config.id}`
                  : "Configure your AI-powered support chat widget"}
              </p>
            </div>
            {isDirty && <Badge variant="secondary">Unsaved Changes</Badge>}
            {config.isPublished && <Badge variant="default">Published</Badge>}
          </div>

          <div className="flex items-center gap-2">
            <input
              type="file"
              accept=".json"
              onChange={handleImport}
              className="hidden"
              id="import-config"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => document.getElementById("import-config")?.click()}
            >
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
              disabled={!config.id}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            {config.id && (
              <>
                <Button variant="outline" size="sm" onClick={handleDuplicate}>
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  className="text-destructive hover:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </Button>
              </>
            )}
            {config.id ? (
              <Button onClick={handleSave} disabled={isLoading || !isDirty}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Saving..." : "Save Changes"}
              </Button>
            ) : (
              <Button onClick={handleCreateNew} disabled={isLoading}>
                <Save className="h-4 w-4 mr-2" />
                {isLoading ? "Creating..." : "Create Widget"}
              </Button>
            )}
            {config.id &&
              (config.isPublished ? (
                <Button
                  variant="outline"
                  onClick={handleUnpublish}
                  disabled={isLoading}
                >
                  <Pause className="h-4 w-4 mr-2" />
                  Unpublish
                </Button>
              ) : (
                <Button
                  onClick={handlePublish}
                  disabled={isLoading || validationErrors.length > 0}
                >
                  <Play className="h-4 w-4 mr-2" />
                  Publish
                </Button>
              ))}
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row gap-6 p-6">
        {/* Configuration Panel */}
        <div className="flex-1 flex flex-col">
          <Card className="flex-1">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Configuration
                  </CardTitle>
                  <CardDescription>
                    Customize your widget settings and appearance
                  </CardDescription>
                </div>
              </div>

              {validationErrors.length > 0 && (
                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="font-medium mb-2">
                      Configuration Issues:
                    </div>
                    <ul className="list-disc list-inside space-y-1">
                      {validationErrors.map((error, index) => (
                        <li key={index} className="text-sm">
                          {error}
                        </li>
                      ))}
                    </ul>
                  </AlertDescription>
                </Alert>
              )}
            </CardHeader>
            <CardContent>
              <Tabs
                value={activeTab}
                onValueChange={setActiveTab}
                className="w-full"
              >
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger
                    value="basic"
                    className="flex items-center gap-2"
                  >
                    <Settings className="h-4 w-4" />
                    Basic
                  </TabsTrigger>
                  <TabsTrigger
                    value="styling"
                    className="flex items-center gap-2"
                  >
                    <Palette className="h-4 w-4" />
                    Styling
                  </TabsTrigger>
                  <TabsTrigger
                    value="behavior"
                    className="flex items-center gap-2"
                  >
                    <Zap className="h-4 w-4" />
                    Behavior
                  </TabsTrigger>
                  <TabsTrigger
                    value="positioning"
                    className="flex items-center gap-2"
                  >
                    <Globe className="h-4 w-4" />
                    Position
                  </TabsTrigger>
                  <TabsTrigger value="advanced">Advanced</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="widgetName">Widget Name *</Label>
                      <Input
                        id="widgetName"
                        placeholder="e.g., Customer Support Chat"
                        value={config.basic.name}
                        onChange={(e) =>
                          updateConfigSection("basic", "name", e.target.value)
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="widgetDescription">Description</Label>
                      <Input
                        id="widgetDescription"
                        placeholder="Brief description of the widget"
                        value={config.basic.description || ""}
                        onChange={(e) =>
                          updateConfigSection(
                            "basic",
                            "description",
                            e.target.value,
                          )
                        }
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="aiProvider">AI Provider *</Label>
                    <Select
                      value={config.basic.aiProvider}
                      onValueChange={(value) =>
                        updateConfigSection("basic", "aiProvider", value)
                      }
                    >
                      <SelectTrigger id="aiProvider">
                        <SelectValue placeholder="Select AI provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableProviders.map((provider) => (
                          <SelectItem key={provider.id} value={provider.slug}>
                            <div className="flex items-center gap-2">
                              <span>{provider.name}</span>
                              <Badge
                                variant={
                                  provider.status === "active"
                                    ? "default"
                                    : "secondary"
                                }
                              >
                                {provider.status}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="aiModel">AI Model *</Label>
                    <Select
                      value={config.basic.aiModel}
                      onValueChange={(value) =>
                        updateConfigSection("basic", "aiModel", value)
                      }
                      disabled={
                        !config.basic.aiProvider || availableModels.length === 0
                      }
                    >
                      <SelectTrigger id="aiModel">
                        <SelectValue placeholder="Select AI model" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableModels.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            <div className="flex flex-col">
                              <span>{model.name}</span>
                              {model.description && (
                                <span className="text-xs text-muted-foreground">
                                  {model.description}
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="temperature">
                      Temperature: {config.basic.temperature}
                    </Label>
                    <Slider
                      id="temperature"
                      min={0}
                      max={2}
                      step={0.1}
                      value={[config.basic.temperature]}
                      onValueChange={(value) =>
                        updateConfigSection("basic", "temperature", value[0])
                      }
                    />
                    <div className="text-xs text-muted-foreground">
                      Lower values make responses more focused, higher values
                      more creative
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="systemPrompt">System Prompt</Label>
                    <Textarea
                      id="systemPrompt"
                      placeholder="You are a helpful customer support assistant..."
                      value={config.basic.systemPrompt}
                      onChange={(e) =>
                        updateConfigSection(
                          "basic",
                          "systemPrompt",
                          e.target.value,
                        )
                      }
                      rows={4}
                    />
                    <div className="text-xs text-muted-foreground">
                      This defines how the AI should behave and respond to users
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="maxTokens">Max Tokens</Label>
                      <Input
                        id="maxTokens"
                        type="number"
                        min={1}
                        max={4000}
                        value={config.basic.maxTokens}
                        onChange={(e) =>
                          updateConfigSection(
                            "basic",
                            "maxTokens",
                            parseInt(e.target.value),
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="responseTimeout">
                        Response Timeout (ms)
                      </Label>
                      <Input
                        id="responseTimeout"
                        type="number"
                        min={1000}
                        max={30000}
                        value={config.basic.responseTimeout}
                        onChange={(e) =>
                          updateConfigSection(
                            "basic",
                            "responseTimeout",
                            parseInt(e.target.value),
                          )
                        }
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="isActive">Widget Active</Label>
                    <Switch
                      id="isActive"
                      checked={config.basic.isActive}
                      onCheckedChange={(checked) =>
                        updateConfigSection("basic", "isActive", checked)
                      }
                    />
                  </div>
                </TabsContent>

                <TabsContent value="styling" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="primaryColor">Primary Color</Label>
                      <ColorPicker
                        color={config.styling.primaryColor}
                        onChange={(value) =>
                          updateConfigSection("styling", "primaryColor", value)
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="secondaryColor">Secondary Color</Label>
                      <ColorPicker
                        color={config.styling.secondaryColor}
                        onChange={(value) =>
                          updateConfigSection(
                            "styling",
                            "secondaryColor",
                            value,
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="backgroundColor">Background Color</Label>
                      <ColorPicker
                        color={config.styling.backgroundColor}
                        onChange={(value) =>
                          updateConfigSection(
                            "styling",
                            "backgroundColor",
                            value,
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="textColor">Text Color</Label>
                      <ColorPicker
                        color={config.styling.textColor}
                        onChange={(value) =>
                          updateConfigSection("styling", "textColor", value)
                        }
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="fontFamily">Font Family</Label>
                      <Select
                        value={config.styling.fontFamily}
                        onValueChange={(value) =>
                          updateConfigSection("styling", "fontFamily", value)
                        }
                      >
                        <SelectTrigger id="fontFamily">
                          <SelectValue placeholder="Select font family" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Inter">Inter</SelectItem>
                          <SelectItem value="Roboto">Roboto</SelectItem>
                          <SelectItem value="Open Sans">Open Sans</SelectItem>
                          <SelectItem value="Lato">Lato</SelectItem>
                          <SelectItem value="Poppins">Poppins</SelectItem>
                          <SelectItem value="Montserrat">Montserrat</SelectItem>
                          <SelectItem value="Source Sans Pro">
                            Source Sans Pro
                          </SelectItem>
                          <SelectItem value="Nunito">Nunito</SelectItem>
                          <SelectItem value="Raleway">Raleway</SelectItem>
                          <SelectItem value="Ubuntu">Ubuntu</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="fontSize">
                        Font Size: {config.styling.fontSize}px
                      </Label>
                      <Slider
                        id="fontSize"
                        min={12}
                        max={20}
                        step={1}
                        value={[config.styling.fontSize]}
                        onValueChange={(value) =>
                          updateConfigSection("styling", "fontSize", value[0])
                        }
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="borderRadius">
                        Border Radius: {config.styling.borderRadius}px
                      </Label>
                      <Slider
                        id="borderRadius"
                        min={0}
                        max={50}
                        step={1}
                        value={[config.styling.borderRadius]}
                        onValueChange={(value) =>
                          updateConfigSection(
                            "styling",
                            "borderRadius",
                            value[0],
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="shadowIntensity">Shadow Intensity</Label>
                      <Select
                        value={config.styling.shadowIntensity}
                        onValueChange={(value) =>
                          updateConfigSection(
                            "styling",
                            "shadowIntensity",
                            value,
                          )
                        }
                      >
                        <SelectTrigger id="shadowIntensity">
                          <SelectValue placeholder="Select shadow intensity" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">None</SelectItem>
                          <SelectItem value="sm">Small</SelectItem>
                          <SelectItem value="md">Medium</SelectItem>
                          <SelectItem value="lg">Large</SelectItem>
                          <SelectItem value="xl">Extra Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="animation">Animation Style</Label>
                    <Select
                      value={config.styling.animation}
                      onValueChange={(value) =>
                        updateConfigSection("styling", "animation", value)
                      }
                    >
                      <SelectTrigger id="animation">
                        <SelectValue placeholder="Select animation" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">None</SelectItem>
                        <SelectItem value="fade">Fade</SelectItem>
                        <SelectItem value="slide">Slide</SelectItem>
                        <SelectItem value="bounce">Bounce</SelectItem>
                        <SelectItem value="pulse">Pulse</SelectItem>
                        <SelectItem value="shake">Shake</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="customCSS">Custom CSS</Label>
                    <Textarea
                      id="customCSS"
                      placeholder="/* Add custom CSS styles here */"
                      value={config.styling.customCSS || ""}
                      onChange={(e) =>
                        updateConfigSection(
                          "styling",
                          "customCSS",
                          e.target.value,
                        )
                      }
                      rows={6}
                    />
                    <div className="text-xs text-muted-foreground">
                      Advanced: Add custom CSS to override default styles
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="behavior" className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="welcomeMessage">Welcome Message</Label>
                    <Textarea
                      id="welcomeMessage"
                      placeholder="Hello! How can I help you today?"
                      value={config.behavior.welcomeMessage}
                      onChange={(e) =>
                        updateConfigSection(
                          "behavior",
                          "welcomeMessage",
                          e.target.value,
                        )
                      }
                      rows={2}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="placeholderText">Input Placeholder</Label>
                    <Input
                      id="placeholderText"
                      placeholder="Type your message here..."
                      value={config.behavior.placeholderText}
                      onChange={(e) =>
                        updateConfigSection(
                          "behavior",
                          "placeholderText",
                          e.target.value,
                        )
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="autoOpen">Auto Open Widget</Label>
                    <Switch
                      id="autoOpen"
                      checked={config.behavior.autoOpen}
                      onCheckedChange={(checked) =>
                        updateConfigSection("behavior", "autoOpen", checked)
                      }
                    />
                  </div>

                  {config.behavior.autoOpen && (
                    <div className="space-y-2 ml-4">
                      <Label htmlFor="autoOpenDelay">
                        Auto Open Delay: {config.behavior.autoOpenDelay / 1000}s
                      </Label>
                      <Slider
                        id="autoOpenDelay"
                        min={0}
                        max={60000}
                        step={1000}
                        value={[config.behavior.autoOpenDelay]}
                        onValueChange={(value) =>
                          updateConfigSection(
                            "behavior",
                            "autoOpenDelay",
                            value[0],
                          )
                        }
                      />
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <Label htmlFor="showBranding">Show Branding</Label>
                    <Switch
                      id="showBranding"
                      checked={config.behavior.showBranding}
                      onCheckedChange={(checked) =>
                        updateConfigSection("behavior", "showBranding", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="enableFileUpload">Enable File Upload</Label>
                    <Switch
                      id="enableFileUpload"
                      checked={config.behavior.enableFileUpload}
                      onCheckedChange={(checked) =>
                        updateConfigSection(
                          "behavior",
                          "enableFileUpload",
                          checked,
                        )
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="showTypingIndicator">
                      Show Typing Indicator
                    </Label>
                    <Switch
                      id="showTypingIndicator"
                      checked={config.behavior.showTypingIndicator}
                      onCheckedChange={(checked) =>
                        updateConfigSection(
                          "behavior",
                          "showTypingIndicator",
                          checked,
                        )
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="enableSoundNotifications">
                      Sound Notifications
                    </Label>
                    <Switch
                      id="enableSoundNotifications"
                      checked={config.behavior.enableSoundNotifications}
                      onCheckedChange={(checked) =>
                        updateConfigSection(
                          "behavior",
                          "enableSoundNotifications",
                          checked,
                        )
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="enableEmojis">Enable Emojis</Label>
                    <Switch
                      id="enableEmojis"
                      checked={config.behavior.enableEmojis}
                      onCheckedChange={(checked) =>
                        updateConfigSection("behavior", "enableEmojis", checked)
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="enableMarkdown">Enable Markdown</Label>
                    <Switch
                      id="enableMarkdown"
                      checked={config.behavior.enableMarkdown}
                      onCheckedChange={(checked) =>
                        updateConfigSection(
                          "behavior",
                          "enableMarkdown",
                          checked,
                        )
                      }
                    />
                  </div>
                </TabsContent>

                <TabsContent value="positioning" className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="position">Widget Position</Label>
                    <Select
                      value={config.positioning.position}
                      onValueChange={(value) =>
                        updateConfigSection("positioning", "position", value)
                      }
                    >
                      <SelectTrigger id="position">
                        <SelectValue placeholder="Select position" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bottom-right">
                          Bottom Right
                        </SelectItem>
                        <SelectItem value="bottom-left">Bottom Left</SelectItem>
                        <SelectItem value="top-right">Top Right</SelectItem>
                        <SelectItem value="top-left">Top Left</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="offsetX">
                        Horizontal Offset: {config.positioning.offsetX}px
                      </Label>
                      <Slider
                        id="offsetX"
                        min={0}
                        max={200}
                        step={5}
                        value={[config.positioning.offsetX]}
                        onValueChange={(value) =>
                          updateConfigSection(
                            "positioning",
                            "offsetX",
                            value[0],
                          )
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="offsetY">
                        Vertical Offset: {config.positioning.offsetY}px
                      </Label>
                      <Slider
                        id="offsetY"
                        min={0}
                        max={200}
                        step={5}
                        value={[config.positioning.offsetY]}
                        onValueChange={(value) =>
                          updateConfigSection(
                            "positioning",
                            "offsetY",
                            value[0],
                          )
                        }
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="width">
                        Width: {config.positioning.width}px
                      </Label>
                      <Slider
                        id="width"
                        min={300}
                        max={600}
                        step={10}
                        value={[config.positioning.width]}
                        onValueChange={(value) =>
                          updateConfigSection("positioning", "width", value[0])
                        }
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="height">
                        Height: {config.positioning.height}px
                      </Label>
                      <Slider
                        id="height"
                        min={400}
                        max={800}
                        step={10}
                        value={[config.positioning.height]}
                        onValueChange={(value) =>
                          updateConfigSection("positioning", "height", value[0])
                        }
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="iconStyle">Icon Style</Label>
                    <Select
                      value={config.positioning.iconStyle}
                      onValueChange={(value) =>
                        updateConfigSection("positioning", "iconStyle", value)
                      }
                    >
                      <SelectTrigger id="iconStyle">
                        <SelectValue placeholder="Select icon style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="chat">Chat Bubble</SelectItem>
                        <SelectItem value="message">Message</SelectItem>
                        <SelectItem value="question">Question Mark</SelectItem>
                        <SelectItem value="support">Support</SelectItem>
                        <SelectItem value="help">Help</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="iconSize">
                      Icon Size: {config.positioning.iconSize}px
                    </Label>
                    <Slider
                      id="iconSize"
                      min={40}
                      max={80}
                      step={5}
                      value={[config.positioning.iconSize]}
                      onValueChange={(value) =>
                        updateConfigSection("positioning", "iconSize", value[0])
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="responsiveScaling">
                      Responsive Scaling
                    </Label>
                    <Switch
                      id="responsiveScaling"
                      checked={config.positioning.responsiveScaling}
                      onCheckedChange={(checked) =>
                        updateConfigSection(
                          "positioning",
                          "responsiveScaling",
                          checked,
                        )
                      }
                    />
                  </div>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="provider">AI Provider</Label>
                    <Select
                      value={config.basic.aiProvider}
                      onValueChange={(value) =>
                        updateConfigSection("basic", "aiProvider", value)
                      }
                    >
                      <SelectTrigger id="provider">
                        <SelectValue placeholder="Select AI provider" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="openai">OpenAI</SelectItem>
                        <SelectItem value="groq">Groq</SelectItem>
                        <SelectItem value="gemini">Gemini</SelectItem>
                        <SelectItem value="mistral">Mistral</SelectItem>
                        <SelectItem value="deepseek">DeepSeek</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="model">Model</Label>
                    <Select
                      value={config.basic.aiModel}
                      onValueChange={(value) =>
                        updateConfigSection("basic", "aiModel", value)
                      }
                    >
                      <SelectTrigger id="model">
                        <SelectValue placeholder="Select model" />
                      </SelectTrigger>
                      <SelectContent>
                        {config.basic.aiProvider === "openai" && (
                          <>
                            <SelectItem value="gpt-4">GPT-4</SelectItem>
                            <SelectItem value="gpt-4-turbo">
                              GPT-4 Turbo
                            </SelectItem>
                            <SelectItem value="gpt-3.5-turbo">
                              GPT-3.5 Turbo
                            </SelectItem>
                          </>
                        )}
                        {config.basic.aiProvider === "groq" && (
                          <>
                            <SelectItem value="llama3-70b">
                              Llama 3 70B
                            </SelectItem>
                            <SelectItem value="llama3-8b">
                              Llama 3 8B
                            </SelectItem>
                            <SelectItem value="mixtral-8x7b">
                              Mixtral 8x7B
                            </SelectItem>
                          </>
                        )}
                        {config.basic.aiProvider === "gemini" && (
                          <>
                            <SelectItem value="gemini-pro">
                              Gemini Pro
                            </SelectItem>
                            <SelectItem value="gemini-ultra">
                              Gemini Ultra
                            </SelectItem>
                          </>
                        )}
                        {config.basic.aiProvider === "mistral" && (
                          <>
                            <SelectItem value="mistral-large">
                              Mistral Large
                            </SelectItem>
                            <SelectItem value="mistral-medium">
                              Mistral Medium
                            </SelectItem>
                            <SelectItem value="mistral-small">
                              Mistral Small
                            </SelectItem>
                          </>
                        )}
                        {config.basic.aiProvider === "deepseek" && (
                          <>
                            <SelectItem value="deepseek-coder">
                              DeepSeek Coder
                            </SelectItem>
                            <SelectItem value="deepseek-chat">
                              DeepSeek Chat
                            </SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="temperature">
                      Temperature: {config.basic.temperature}
                    </Label>
                    <Slider
                      id="temperature"
                      min={0}
                      max={1}
                      step={0.1}
                      value={[config.basic.temperature]}
                      onValueChange={(value) =>
                        updateConfigSection("basic", "temperature", value[0])
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="systemPrompt">System Prompt</Label>
                    <Textarea
                      id="systemPrompt"
                      placeholder="You are a helpful customer support assistant..."
                      value={config.basic.systemPrompt}
                      onChange={(e) =>
                        updateConfigSection(
                          "basic",
                          "systemPrompt",
                          e.target.value,
                        )
                      }
                      rows={4}
                    />
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button variant="outline" onClick={resetConfig}>
                Reset to Default
              </Button>
              <div className="flex gap-2">
                {config.id && (
                  <Button
                    variant="outline"
                    onClick={() =>
                      navigate(`/admin/widgets/${config.id}/preview`)
                    }
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                )}
                <Button onClick={handleSave} disabled={isLoading || !isDirty}>
                  <Save className="h-4 w-4 mr-2" />
                  {isLoading ? "Saving..." : "Save Configuration"}
                </Button>
              </div>
            </CardFooter>
          </Card>

          {/* Code Generation Section */}
          {config.id && config.isPublished && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Code className="h-5 w-5" />
                  Embed Code
                </CardTitle>
                <CardDescription>
                  Copy and paste this code to embed the widget on your website
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2 mb-4">
                  <Button
                    variant={codeType === "iframe" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCodeType("iframe")}
                  >
                    <Code className="mr-2 h-4 w-4" /> iFrame
                  </Button>
                  <Button
                    variant={codeType === "script" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCodeType("script")}
                  >
                    <Code className="mr-2 h-4 w-4" /> JavaScript
                  </Button>
                  <Button
                    variant={codeType === "react" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCodeType("react")}
                  >
                    <Code className="mr-2 h-4 w-4" /> React
                  </Button>
                  <Button
                    variant={codeType === "vue" ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCodeType("vue")}
                  >
                    <Code className="mr-2 h-4 w-4" /> Vue
                  </Button>
                </div>

                <div className="relative">
                  <pre className="bg-muted p-4 rounded-md overflow-x-auto max-h-64">
                    <code className="text-sm">{getEmbedCode()}</code>
                  </pre>
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-2 right-2"
                    onClick={copyEmbedCode}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>

                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-md">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    <strong>Note:</strong> This widget is published and ready to
                    embed. The embed code will automatically reflect any future
                    changes you make to the configuration.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Live Preview */}
        <div className="w-full lg:w-1/2">
          <div className="sticky top-6">
            <WidgetPreview config={config} className="h-[800px]" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default WidgetConfigurator;

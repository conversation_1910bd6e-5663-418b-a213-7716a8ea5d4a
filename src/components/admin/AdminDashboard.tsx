import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Plus,
  Search,
  MoreVertical,
  Edit,
  Copy,
  Trash2,
  Eye,
  Play,
  Pause,
  LayoutGrid,
  List,
  Filter,
  Settings,
  BarChart3,
  Users,
  MessageSquare,
  TrendingUp,
  Activity,
  Zap,
  Globe,
  Shield,
  AlertCircle,
  CheckCircle,
  Clock,
  DollarSign,
} from "lucide-react";
import { widgetAPI, aiProviderAPI } from "@/lib/api";
import { WidgetConfig } from "@/types/widget-config";
import { formatDistanceToNow } from "date-fns";
import AdminLayout from "@/components/layout/AdminLayout";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface DashboardStats {
  totalWidgets: number;
  publishedWidgets: number;
  totalConversations: number;
  avgResponseTime: number;
  userSatisfaction: number;
  monthlyGrowth: number;
  totalRevenue: number;
  activeUsers: number;
}

interface RecentActivity {
  id: string;
  type:
    | "widget_created"
    | "widget_published"
    | "conversation_started"
    | "provider_added";
  message: string;
  timestamp: Date;
  user?: string;
}

interface AIProvider {
  id: string;
  name: string;
  slug: string;
  status: "active" | "inactive" | "error";
  is_configured: boolean;
  models_count: number;
  last_used?: Date;
}

interface PerformanceMetric {
  name: string;
  value: number;
  change: number;
  trend: "up" | "down" | "stable";
}

const AdminDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [widgets, setWidgets] = useState<WidgetConfig[]>([]);
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "published" | "draft"
  >("all");
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState<DashboardStats>({
    totalWidgets: 0,
    publishedWidgets: 0,
    totalConversations: 0,
    avgResponseTime: 0,
    userSatisfaction: 0,
    monthlyGrowth: 0,
    totalRevenue: 0,
    activeUsers: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<
    PerformanceMetric[]
  >([]);

  // Load all dashboard data on mount
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load widgets
      const widgetsResponse = await widgetAPI.getWidgets();
      setWidgets(widgetsResponse);

      // Load AI providers
      const providersResponse = await aiProviderAPI.getProviders();
      setProviders(providersResponse);

      // Calculate stats from real data
      const publishedCount = widgetsResponse.filter(
        (w) => w.isPublished,
      ).length;
      setStats({
        totalWidgets: widgetsResponse.length,
        publishedWidgets: publishedCount,
        totalConversations: await getTotalConversations(),
        avgResponseTime: await getAverageResponseTime(),
        userSatisfaction: await getUserSatisfactionScore(),
        monthlyGrowth: await getMonthlyGrowth(),
        totalRevenue: await getTotalRevenue(),
        activeUsers: await getActiveUsers(),
      });

      // Load recent activity
      setRecentActivity(await getRecentActivity());

      // Load performance metrics
      setPerformanceMetrics(await getPerformanceMetrics());
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "Failed to load dashboard data";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // API helper functions
  const getTotalConversations = async (): Promise<number> => {
    try {
      // This would be a real API call to get conversation count
      return 1247; // Placeholder
    } catch {
      return 0;
    }
  };

  const getAverageResponseTime = async (): Promise<number> => {
    try {
      // This would be a real API call to get avg response time
      return 1250; // Placeholder in ms
    } catch {
      return 0;
    }
  };

  const getUserSatisfactionScore = async (): Promise<number> => {
    try {
      // This would be a real API call to get satisfaction score
      return 4.2; // Placeholder out of 5
    } catch {
      return 0;
    }
  };

  const getMonthlyGrowth = async (): Promise<number> => {
    try {
      // This would be a real API call to get growth percentage
      return 15.3; // Placeholder percentage
    } catch {
      return 0;
    }
  };

  const getTotalRevenue = async (): Promise<number> => {
    try {
      // This would be a real API call to get revenue
      return 12450; // Placeholder in dollars
    } catch {
      return 0;
    }
  };

  const getActiveUsers = async (): Promise<number> => {
    try {
      // This would be a real API call to get active users
      return 89; // Placeholder
    } catch {
      return 0;
    }
  };

  const getRecentActivity = async (): Promise<RecentActivity[]> => {
    try {
      // This would be a real API call to get recent activity
      return [
        {
          id: "1",
          type: "widget_created",
          message: 'New widget "Customer Support" created',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          user: "Admin User",
        },
        {
          id: "2",
          type: "widget_published",
          message: 'Widget "Sales Assistant" published',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
          user: "Admin User",
        },
        {
          id: "3",
          type: "provider_added",
          message: "OpenAI provider configured",
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          user: "Admin User",
        },
      ];
    } catch {
      return [];
    }
  };

  const getPerformanceMetrics = async (): Promise<PerformanceMetric[]> => {
    try {
      // This would be a real API call to get performance metrics
      return [
        { name: "Response Time", value: 1.2, change: -8.5, trend: "down" },
        { name: "Success Rate", value: 98.5, change: 2.1, trend: "up" },
        { name: "User Engagement", value: 87.3, change: 5.2, trend: "up" },
        { name: "Error Rate", value: 1.5, change: -12.3, trend: "down" },
      ];
    } catch {
      return [];
    }
  };

  // Filter widgets based on search and status
  const filteredWidgets = widgets.filter((widget) => {
    const matchesSearch =
      widget.basic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (widget.basic.description || "")
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === "all" ||
      (filterStatus === "published" && widget.isPublished) ||
      (filterStatus === "draft" && !widget.isPublished);

    return matchesSearch && matchesStatus;
  });

  const handleDelete = async (widgetId: string) => {
    try {
      await widgetAPI.deleteWidget(widgetId);
      setWidgets(widgets.filter((w) => w.id !== widgetId));
      toast({
        title: "Success",
        description: "Widget deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete widget",
        variant: "destructive",
      });
    }
  };

  const handleDuplicate = async (widgetId: string) => {
    try {
      const duplicatedWidget = await widgetAPI.duplicateWidget(widgetId);
      setWidgets([...widgets, duplicatedWidget]);
      toast({
        title: "Success",
        description: "Widget duplicated successfully",
      });
      navigate(`/admin/widgets/${duplicatedWidget.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate widget",
        variant: "destructive",
      });
    }
  };

  const handlePublish = async (widgetId: string) => {
    try {
      const publishedWidget = await widgetAPI.publishWidget(widgetId);
      setWidgets(widgets.map((w) => (w.id === widgetId ? publishedWidget : w)));
      toast({
        title: "Success",
        description: "Widget published successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to publish widget",
        variant: "destructive",
      });
    }
  };

  const handleUnpublish = async (widgetId: string) => {
    try {
      const unpublishedWidget = await widgetAPI.unpublishWidget(widgetId);
      setWidgets(
        widgets.map((w) => (w.id === widgetId ? unpublishedWidget : w)),
      );
      toast({
        title: "Success",
        description: "Widget unpublished successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unpublish widget",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (widget: WidgetConfig) => {
    if (widget.isPublished) {
      return <Badge variant="default">Published</Badge>;
    }
    return <Badge variant="secondary">Draft</Badge>;
  };

  const StatCard = ({
    title,
    value,
    description,
    icon: Icon,
    trend,
    format = "number",
  }: {
    title: string;
    value: string | number;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
    trend?: number;
    format?: "number" | "currency" | "percentage" | "time";
  }) => {
    const formatValue = (val: string | number) => {
      if (typeof val === "string") return val;

      switch (format) {
        case "currency":
          return `${val.toLocaleString()}`;
        case "percentage":
          return `${val}%`;
        case "time":
          return `${val}ms`;
        default:
          return val.toLocaleString();
      }
    };

    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          <Icon className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatValue(value)}</div>
          <p className="text-xs text-muted-foreground flex items-center gap-1">
            {trend !== undefined && (
              <span
                className={`inline-flex items-center ${
                  trend > 0
                    ? "text-green-600"
                    : trend < 0
                      ? "text-red-600"
                      : "text-gray-600"
                }`}
              >
                <TrendingUp
                  className={`h-3 w-3 mr-1 ${
                    trend < 0 ? "rotate-180" : trend === 0 ? "rotate-90" : ""
                  }`}
                />
                {trend > 0 ? "+" : ""}
                {trend}%
              </span>
            )}
            {description}
          </p>
        </CardContent>
      </Card>
    );
  };

  const WidgetCard = ({ widget }: { widget: WidgetConfig }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{widget.basic.name}</CardTitle>
            <CardDescription className="mt-1">
              {widget.basic.description || "No description provided"}
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => navigate(`/admin/widgets/${widget.id}`)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => navigate(`/admin/widgets/${widget.id}/preview`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicate(widget.id!)}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {widget.isPublished ? (
                <DropdownMenuItem onClick={() => handleUnpublish(widget.id!)}>
                  <Pause className="h-4 w-4 mr-2" />
                  Unpublish
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => handlePublish(widget.id!)}>
                  <Play className="h-4 w-4 mr-2" />
                  Publish
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem
                    className="text-destructive focus:text-destructive"
                    onSelect={(e) => e.preventDefault()}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Widget</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete "{widget.basic.name}"?
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => handleDelete(widget.id!)}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>AI Provider: {widget.basic.aiProvider}</span>
          <span>Model: {widget.basic.aiModel}</span>
        </div>
        <div className="mt-2 flex items-center gap-2">
          {getStatusBadge(widget)}
          <Badge variant="outline">{widget.basic.aiProvider}</Badge>
        </div>
      </CardContent>
      <CardContent className="pt-3 border-t">
        <div className="flex items-center justify-between w-full text-xs text-muted-foreground">
          <span>
            Created{" "}
            {formatDistanceToNow(new Date(widget.createdAt), {
              addSuffix: true,
            })}
          </span>
          <span>
            Updated{" "}
            {formatDistanceToNow(new Date(widget.updatedAt), {
              addSuffix: true,
            })}
          </span>
        </div>
      </CardContent>
    </Card>
  );

  if (error) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">
              Failed to Load Dashboard
            </h2>
            <p className="text-muted-foreground mb-4">{error}</p>
            <Button onClick={loadDashboardData}>Retry</Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6 p-6 bg-background min-h-screen">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Dashboard</h1>
            <p className="text-muted-foreground">
              Manage your AI-powered chat widgets and monitor performance
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => navigate("/admin/providers")}
            >
              <Settings className="h-4 w-4 mr-2" />
              AI Providers
            </Button>
            <Button onClick={() => navigate("/admin/widgets/new")}>
              <Plus className="h-4 w-4 mr-2" />
              Create Widget
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading dashboard...</p>
            </div>
          </div>
        )}

        {!isLoading && (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="Total Widgets"
                value={stats.totalWidgets}
                description="widgets created"
                icon={MessageSquare}
              />
              <StatCard
                title="Published"
                value={stats.publishedWidgets}
                description="widgets live"
                icon={Globe}
                trend={stats.monthlyGrowth}
              />
              <StatCard
                title="Conversations"
                value={stats.totalConversations}
                description="this month"
                icon={Users}
                trend={15}
              />
              <StatCard
                title="Avg Response Time"
                value={stats.avgResponseTime}
                description="response time"
                icon={Zap}
                format="time"
                trend={-8}
              />
            </div>

            {/* Secondary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <StatCard
                title="Revenue"
                value={stats.totalRevenue}
                description="total revenue"
                icon={DollarSign}
                format="currency"
                trend={12.5}
              />
              <StatCard
                title="Active Users"
                value={stats.activeUsers}
                description="active this month"
                icon={Users}
                trend={8.2}
              />
              <StatCard
                title="Satisfaction"
                value={stats.userSatisfaction}
                description="out of 5.0"
                icon={CheckCircle}
                trend={3.1}
              />
              <StatCard
                title="AI Providers"
                value={providers.filter((p) => p.status === "active").length}
                description="providers active"
                icon={Settings}
              />
            </div>

            {/* Main Content Tabs */}
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="widgets">Widgets</TabsTrigger>
                <TabsTrigger value="providers">Providers</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => navigate("/admin/widgets/new")}
                  >
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2">
                        <Plus className="h-5 w-5" />
                        Create New Widget
                      </CardTitle>
                      <CardDescription>
                        Set up a new AI-powered chat widget for your website
                      </CardDescription>
                    </CardHeader>
                  </Card>

                  <Card
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => navigate("/admin/providers")}
                  >
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2">
                        <Settings className="h-5 w-5" />
                        Manage AI Providers
                      </CardTitle>
                      <CardDescription>
                        Configure OpenAI, Gemini, and other AI service providers
                      </CardDescription>
                    </CardHeader>
                  </Card>

                  <Card className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        View Analytics
                      </CardTitle>
                      <CardDescription>
                        Monitor widget performance and user interactions
                      </CardDescription>
                    </CardHeader>
                  </Card>
                </div>

                {/* Performance Metrics */}
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Metrics</CardTitle>
                    <CardDescription>
                      Key performance indicators for your chat widgets
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {performanceMetrics.map((metric, index) => (
                        <div key={index} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">
                              {metric.name}
                            </span>
                            <span
                              className={`text-xs ${
                                metric.trend === "up"
                                  ? "text-green-600"
                                  : metric.trend === "down"
                                    ? "text-red-600"
                                    : "text-gray-600"
                              }`}
                            >
                              {metric.change > 0 ? "+" : ""}
                              {metric.change}%
                            </span>
                          </div>
                          <div className="text-2xl font-bold">
                            {metric.value}%
                          </div>
                          <Progress value={metric.value} className="h-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="widgets" className="space-y-4">
                {/* Widgets Section */}
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold">Your Widgets</h2>
                    <p className="text-muted-foreground">
                      Manage and configure your chat widgets
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => navigate("/admin/widgets")}
                  >
                    View All
                  </Button>
                </div>

                {/* Filters and Search */}
                <div className="flex items-center justify-between gap-4">
                  <div className="flex items-center gap-4 flex-1">
                    <div className="relative flex-1 max-w-sm">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search widgets..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Filter className="h-4 w-4 mr-2" />
                          {filterStatus === "all"
                            ? "All"
                            : filterStatus === "published"
                              ? "Published"
                              : "Draft"}
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem
                          onClick={() => setFilterStatus("all")}
                        >
                          All Widgets
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterStatus("published")}
                        >
                          Published Only
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => setFilterStatus("draft")}
                        >
                          Drafts Only
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant={viewMode === "grid" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setViewMode("grid")}
                    >
                      <LayoutGrid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === "list" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setViewMode("list")}
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Widget List */}
                {filteredWidgets.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
                      <Plus className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">
                      {searchQuery || filterStatus !== "all"
                        ? "No widgets found"
                        : "No widgets yet"}
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      {searchQuery || filterStatus !== "all"
                        ? "Try adjusting your search or filter criteria"
                        : "Create your first AI-powered chat widget to get started"}
                    </p>
                    {!searchQuery && filterStatus === "all" && (
                      <Button onClick={() => navigate("/admin/widgets/new")}>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Your First Widget
                      </Button>
                    )}
                  </div>
                ) : (
                  <div
                    className={
                      viewMode === "grid"
                        ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                        : "space-y-4"
                    }
                  >
                    {filteredWidgets.slice(0, 6).map((widget) => (
                      <WidgetCard key={widget.id} widget={widget} />
                    ))}
                  </div>
                )}

                {filteredWidgets.length > 6 && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => navigate("/admin/widgets")}
                    >
                      View All {filteredWidgets.length} Widgets
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="providers" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold">AI Providers</h2>
                    <p className="text-muted-foreground">
                      Manage your AI service providers and their configurations
                    </p>
                  </div>
                  <Button onClick={() => navigate("/admin/providers")}>
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Providers
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {providers.map((provider) => (
                    <Card
                      key={provider.id}
                      className="hover:shadow-md transition-shadow"
                    >
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <CardTitle className="text-lg">
                            {provider.name}
                          </CardTitle>
                          <Badge
                            variant={
                              provider.status === "active"
                                ? "default"
                                : provider.status === "error"
                                  ? "destructive"
                                  : "secondary"
                            }
                          >
                            {provider.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">
                              Models:
                            </span>
                            <span className="font-medium">
                              {provider.models_count}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-muted-foreground">
                              Configured:
                            </span>
                            <span
                              className={`font-medium ${
                                provider.is_configured
                                  ? "text-green-600"
                                  : "text-red-600"
                              }`}
                            >
                              {provider.is_configured ? "Yes" : "No"}
                            </span>
                          </div>
                          {provider.last_used && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-muted-foreground">
                                Last used:
                              </span>
                              <span className="font-medium">
                                {formatDistanceToNow(provider.last_used, {
                                  addSuffix: true,
                                })}
                              </span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {providers.length === 0 && (
                  <div className="text-center py-12">
                    <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">
                      No AI Providers
                    </h3>
                    <p className="text-muted-foreground mb-4">
                      Configure your first AI provider to start creating widgets
                    </p>
                    <Button onClick={() => navigate("/admin/providers")}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Provider
                    </Button>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="activity" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold">Recent Activity</h2>
                    <p className="text-muted-foreground">
                      Latest actions and events in your dashboard
                    </p>
                  </div>
                </div>

                <Card>
                  <CardContent className="p-0">
                    {recentActivity.length > 0 ? (
                      <div className="divide-y">
                        {recentActivity.map((activity) => (
                          <div
                            key={activity.id}
                            className="p-4 flex items-center gap-4"
                          >
                            <div
                              className={`p-2 rounded-full ${
                                activity.type === "widget_created"
                                  ? "bg-blue-100 text-blue-600"
                                  : activity.type === "widget_published"
                                    ? "bg-green-100 text-green-600"
                                    : activity.type === "provider_added"
                                      ? "bg-purple-100 text-purple-600"
                                      : "bg-gray-100 text-gray-600"
                              }`}
                            >
                              {activity.type === "widget_created" && (
                                <Plus className="h-4 w-4" />
                              )}
                              {activity.type === "widget_published" && (
                                <Globe className="h-4 w-4" />
                              )}
                              {activity.type === "provider_added" && (
                                <Settings className="h-4 w-4" />
                              )}
                              {activity.type === "conversation_started" && (
                                <MessageSquare className="h-4 w-4" />
                              )}
                            </div>
                            <div className="flex-1">
                              <p className="font-medium">{activity.message}</p>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                {formatDistanceToNow(activity.timestamp, {
                                  addSuffix: true,
                                })}
                                {activity.user && (
                                  <>
                                    <span>•</span>
                                    <span>{activity.user}</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                          No Recent Activity
                        </h3>
                        <p className="text-muted-foreground">
                          Activity will appear here as you use the dashboard
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;

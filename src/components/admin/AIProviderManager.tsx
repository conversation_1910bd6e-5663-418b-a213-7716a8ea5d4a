import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogT<PERSON>le,
  Alert<PERSON><PERSON>ogTrigger,
} from "@/components/ui/alert-dialog";
import {
  LayoutGrid,
  List,
  Plus,
  Edit,
  Trash2,
  RefreshCw,
  Check,
  X,
  AlertCircle,
  CheckCircle,
  Loader2,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { aiProviderAPI } from "@/lib/api";
import { AIProvider, AIModel } from "@/types/widget-config";

const AIProviderManager = () => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [activeTab, setActiveTab] = useState("providers");
  const [providers, setProviders] = useState<AIProvider[]>([]);
  const [availableModels, setAvailableModels] = useState<AIModel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const [isTestingProvider, setIsTestingProvider] = useState<string | null>(
    null,
  );
  const { toast } = useToast();

  const [newProvider, setNewProvider] = useState({
    name: "",
    slug: "",
    apiKey: "",
    providerType: "",
  });

  // Load providers on component mount
  useEffect(() => {
    loadProviders();
    loadAvailableModels();
  }, []);

  const loadProviders = async () => {
    try {
      setIsLoading(true);
      const providersData = await aiProviderAPI.getProviders();
      setProviders(providersData);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load AI providers",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const loadAvailableModels = async () => {
    try {
      const modelsData = await aiProviderAPI.getAvailableModels();
      setAvailableModels(modelsData);
    } catch (error) {
      console.error("Failed to load available models:", error);
    }
  };

  const handleFetchModels = async (providerId: string) => {
    setIsFetching(true);
    try {
      const models = await aiProviderAPI.fetchModels(providerId);
      // Update the provider with new models
      setProviders((prev) =>
        prev.map((p) => (p.id === providerId ? { ...p, models: models } : p)),
      );
      toast({
        title: "Success",
        description: `Fetched ${models.length} models successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch models",
        variant: "destructive",
      });
    } finally {
      setIsFetching(false);
    }
  };

  const handleTestProvider = async (providerId: string) => {
    setIsTestingProvider(providerId);
    try {
      const result = await aiProviderAPI.testProvider(providerId);
      if (result.success) {
        toast({
          title: "Success",
          description: "Provider connection test successful",
        });
        // Update provider status
        setProviders((prev) =>
          prev.map((p) =>
            p.id === providerId
              ? {
                  ...p,
                  status: "active",
                  last_tested_at: new Date().toISOString(),
                }
              : p,
          ),
        );
      } else {
        toast({
          title: "Test Failed",
          description: result.message,
          variant: "destructive",
        });
        // Update provider status to error
        setProviders((prev) =>
          prev.map((p) =>
            p.id === providerId ? { ...p, status: "error" } : p,
          ),
        );
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test provider connection",
        variant: "destructive",
      });
    } finally {
      setIsTestingProvider(null);
    }
  };

  const handleAddProvider = async () => {
    if (!newProvider.name || !newProvider.apiKey || !newProvider.providerType) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const providerData = {
        name: newProvider.name,
        slug:
          newProvider.slug ||
          newProvider.name.toLowerCase().replace(/\s+/g, "-"),
        api_key: newProvider.apiKey,
        settings: {
          provider_type: newProvider.providerType,
        },
      };

      const createdProvider = await aiProviderAPI.createProvider(providerData);
      setProviders((prev) => [...prev, createdProvider]);
      setNewProvider({ name: "", slug: "", apiKey: "", providerType: "" });
      setActiveTab("providers");

      toast({
        title: "Success",
        description: "AI provider added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add AI provider",
        variant: "destructive",
      });
    }
  };

  const handleDeleteProvider = async (id: string) => {
    try {
      await aiProviderAPI.deleteProvider(id);
      setProviders((prev) => prev.filter((p) => p.id !== id));
      toast({
        title: "Success",
        description: "Provider deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete provider",
        variant: "destructive",
      });
    }
  };

  const handleToggleModelStatus = async (modelId: string, enabled: boolean) => {
    try {
      if (enabled) {
        await aiProviderAPI.enableModel(modelId);
      } else {
        await aiProviderAPI.disableModel(modelId);
      }

      // Update local state
      setAvailableModels((prev) =>
        prev.map((m) =>
          m.id === modelId ? { ...m, is_available: enabled } : m,
        ),
      );

      // Update providers state
      setProviders((prev) =>
        prev.map((p) => ({
          ...p,
          models: p.models.map((m) =>
            m.id === modelId ? { ...m, is_available: enabled } : m,
          ),
        })),
      );

      toast({
        title: "Success",
        description: `Model ${enabled ? "enabled" : "disabled"} successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${enabled ? "enable" : "disable"} model`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="bg-background p-6 w-full">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">AI Provider Management</h1>
          <p className="text-muted-foreground">
            Configure and manage your AI providers and models
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <LayoutGrid className="h-4 w-4 mr-1" />
            Grid
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="h-4 w-4 mr-1" />
            List
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="add-provider">Add Provider</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {providers.map((provider) => (
                <Card key={provider.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <CardTitle>{provider.name}</CardTitle>
                      <Badge
                        variant={
                          provider.status === "active"
                            ? "default"
                            : provider.status === "error"
                              ? "destructive"
                              : "secondary"
                        }
                      >
                        {provider.status}
                      </Badge>
                    </div>
                    <CardDescription className="mt-1">
                      {provider.api_key_required
                        ? "API Key Required"
                        : "No API Key Required"}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">
                      <p className="font-medium">
                        Available Models: {provider.models.length}
                      </p>
                      <p className="text-muted-foreground mt-1">
                        Available:{" "}
                        {provider.models.filter((m) => m.is_available).length}
                      </p>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between pt-2">
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleFetchModels(provider.id)}
                        disabled={isFetching}
                      >
                        <RefreshCw
                          className={`h-4 w-4 mr-1 ${isFetching ? "animate-spin" : ""}`}
                        />
                        Fetch Models
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestProvider(provider.id)}
                        disabled={isTestingProvider === provider.id}
                      >
                        {isTestingProvider === provider.id ? (
                          <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                        ) : provider.status === "active" ? (
                          <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
                        ) : (
                          <AlertCircle className="h-4 w-4 mr-1 text-red-500" />
                        )}
                        Test
                      </Button>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Provider</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete {provider.name}?
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteProvider(provider.id)}
                              className="bg-destructive text-destructive-foreground"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>API Key</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Models</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {providers.map((provider) => (
                      <TableRow key={provider.id}>
                        <TableCell className="font-medium">
                          {provider.name}
                        </TableCell>
                        <TableCell>
                          {provider.api_key_required
                            ? "Required"
                            : "Not Required"}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              provider.status === "active"
                                ? "default"
                                : provider.status === "error"
                                  ? "destructive"
                                  : "secondary"
                            }
                          >
                            {provider.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {provider.models.length} (
                          {provider.models.filter((m) => m.is_available).length}{" "}
                          available)
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleFetchModels(provider.id)}
                              disabled={isFetching}
                            >
                              <RefreshCw
                                className={`h-4 w-4 ${isFetching ? "animate-spin" : ""}`}
                              />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleTestProvider(provider.id)}
                              disabled={isTestingProvider === provider.id}
                            >
                              {isTestingProvider === provider.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : provider.status === "active" ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <AlertCircle className="h-4 w-4 text-red-500" />
                              )}
                            </Button>
                            <Button variant="ghost" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <Trash2 className="h-4 w-4 text-destructive" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Delete Provider
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete{" "}
                                    {provider.name}? This action cannot be
                                    undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() =>
                                      handleDeleteProvider(provider.id)
                                    }
                                    className="bg-destructive text-destructive-foreground"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="add-provider">
          <Card>
            <CardHeader>
              <CardTitle>Add New AI Provider</CardTitle>
              <CardDescription>
                Configure a new AI provider by entering the required information
                below.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="provider-name">Provider Name *</Label>
                  <Input
                    id="provider-name"
                    placeholder="e.g., OpenAI, Gemini, Mistral"
                    value={newProvider.name}
                    onChange={(e) => {
                      const name = e.target.value;
                      const slug = name
                        .toLowerCase()
                        .replace(/\s+/g, "-")
                        .replace(/[^a-z0-9-]/g, "");
                      setNewProvider({ ...newProvider, name, slug });
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="provider-slug">Provider Slug *</Label>
                  <Input
                    id="provider-slug"
                    placeholder="e.g., openai, gemini, mistral"
                    value={newProvider.slug}
                    onChange={(e) =>
                      setNewProvider({ ...newProvider, slug: e.target.value })
                    }
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="provider-type">Provider Type *</Label>
                <Select
                  value={newProvider.providerType}
                  onValueChange={(value) =>
                    setNewProvider({ ...newProvider, providerType: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select provider type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="openai">OpenAI</SelectItem>
                    <SelectItem value="gemini">Gemini</SelectItem>
                    <SelectItem value="mistral">Mistral</SelectItem>
                    <SelectItem value="anthropic">Anthropic</SelectItem>
                    <SelectItem value="huggingface">HuggingFace</SelectItem>
                    <SelectItem value="deepseek">DeepSeek</SelectItem>
                    <SelectItem value="groq">Groq</SelectItem>
                    <SelectItem value="openrouter">OpenRouter</SelectItem>
                    <SelectItem value="custom">Custom</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="api-key">API Key *</Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="Enter your API key"
                  value={newProvider.apiKey}
                  onChange={(e) =>
                    setNewProvider({ ...newProvider, apiKey: e.target.value })
                  }
                />
                <div className="text-xs text-muted-foreground">
                  Your API key will be encrypted and stored securely
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="test-connection" />
                <Label htmlFor="test-connection">
                  Test connection before saving
                </Label>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button
                variant="outline"
                onClick={() => setActiveTab("providers")}
              >
                Cancel
              </Button>
              <Button
                onClick={handleAddProvider}
                disabled={
                  !newProvider.name ||
                  !newProvider.apiKey ||
                  !newProvider.providerType
                }
              >
                Add Provider
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="models">
          <Card>
            <CardHeader>
              <CardTitle>Available AI Models</CardTitle>
              <CardDescription>
                Manage which AI models are enabled for use in your chat widgets.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading models...</span>
                </div>
              ) : viewMode === "grid" ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {availableModels.map((model) => (
                    <Card key={model.id} className="border">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-base">
                            {model.name}
                          </CardTitle>
                          <Badge>{model.provider}</Badge>
                        </div>
                        {model.description && (
                          <CardDescription className="text-sm">
                            {model.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>
                            Max Tokens: {model.max_tokens.toLocaleString()}
                          </div>
                          <div>
                            Streaming: {model.supports_streaming ? "Yes" : "No"}
                          </div>
                          {model.cost_per_token && (
                            <div>Cost: ${model.cost_per_token}/token</div>
                          )}
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between pt-2">
                        <div className="flex items-center space-x-2">
                          <Switch
                            id={`model-${model.id}`}
                            checked={model.is_available}
                            onCheckedChange={(checked) =>
                              handleToggleModelStatus(model.id, checked)
                            }
                          />
                          <Label htmlFor={`model-${model.id}`}>
                            {model.is_available ? "Available" : "Disabled"}
                          </Label>
                        </div>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Model Name</TableHead>
                      <TableHead>Provider</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {availableModels.map((model) => (
                      <TableRow key={model.id}>
                        <TableCell className="font-medium">
                          <div>
                            <div>{model.name}</div>
                            {model.description && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {model.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{model.provider}</TableCell>
                        <TableCell>
                          {model.is_available ? (
                            <Badge variant="default" className="bg-green-500">
                              <Check className="h-3 w-3 mr-1" /> Available
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              <X className="h-3 w-3 mr-1" /> Disabled
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <Switch
                            id={`table-model-${model.id}`}
                            checked={model.is_available}
                            onCheckedChange={(checked) =>
                              handleToggleModelStatus(model.id, checked)
                            }
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIProviderManager;

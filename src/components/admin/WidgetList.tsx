import React, { useState, useEffect } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Plus,
  Search,
  MoreVertical,
  <PERSON>,
  Co<PERSON>,
  Trash2,
  <PERSON>,
  <PERSON>,
  Pause,
  LayoutGrid,
  List,
  Filter,
} from "lucide-react";
import {
  useWidgets,
  useWidgetActions,
  useIsLoading,
  useError,
} from "@/lib/widget-config-store";
import { WidgetConfig } from "@/types/widget-config";
import { formatDistanceToNow } from "date-fns";

interface WidgetListProps {
  className?: string;
}

const WidgetList: React.FC<WidgetListProps> = ({ className }) => {
  const navigate = useNavigate();
  const widgets = useWidgets();
  const isLoading = useIsLoading();
  const error = useError();
  const { toast } = useToast();

  const {
    loadWidgets,
    deleteWidget,
    duplicateWidget,
    publishWidget,
    unpublishWidget,
  } = useWidgetActions();

  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "published" | "draft"
  >("all");

  // Load widgets on mount
  useEffect(() => {
    loadWidgets().catch(console.error);
  }, [loadWidgets]);

  // Filter widgets based on search and status
  const filteredWidgets = widgets.filter((widget) => {
    const matchesSearch =
      widget.basic.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (widget.basic.description || "")
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === "all" ||
      (filterStatus === "published" && widget.isPublished) ||
      (filterStatus === "draft" && !widget.isPublished);

    return matchesSearch && matchesStatus;
  });

  const handleDelete = async (widgetId: string) => {
    try {
      await deleteWidget(widgetId);
      toast({
        title: "Success",
        description: "Widget deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete widget",
        variant: "destructive",
      });
    }
  };

  const handleDuplicate = async (widgetId: string) => {
    try {
      const duplicatedWidget = await duplicateWidget(widgetId);
      toast({
        title: "Success",
        description: "Widget duplicated successfully",
      });
      navigate(`/admin/widgets/${duplicatedWidget.id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to duplicate widget",
        variant: "destructive",
      });
    }
  };

  const handlePublish = async (widgetId: string) => {
    try {
      await publishWidget(widgetId);
      toast({
        title: "Success",
        description: "Widget published successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to publish widget",
        variant: "destructive",
      });
    }
  };

  const handleUnpublish = async (widgetId: string) => {
    try {
      await unpublishWidget(widgetId);
      toast({
        title: "Success",
        description: "Widget unpublished successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to unpublish widget",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (widget: WidgetConfig) => {
    if (widget.isPublished) {
      return <Badge variant="default">Published</Badge>;
    }
    return <Badge variant="secondary">Draft</Badge>;
  };

  const WidgetCard = ({ widget }: { widget: WidgetConfig }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{widget.basic.name}</CardTitle>
            <CardDescription className="mt-1">
              {widget.basic.description || "No description provided"}
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={() => navigate(`/admin/widgets/${widget.id}`)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => navigate(`/admin/widgets/${widget.id}/preview`)}
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDuplicate(widget.id!)}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {widget.isPublished ? (
                <DropdownMenuItem onClick={() => handleUnpublish(widget.id!)}>
                  <Pause className="h-4 w-4 mr-2" />
                  Unpublish
                </DropdownMenuItem>
              ) : (
                <DropdownMenuItem onClick={() => handlePublish(widget.id!)}>
                  <Play className="h-4 w-4 mr-2" />
                  Publish
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem
                    className="text-destructive focus:text-destructive"
                    onSelect={(e) => e.preventDefault()}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Widget</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete "{widget.basic.name}"?
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={() => handleDelete(widget.id!)}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>AI Provider: {widget.basic.aiProvider}</span>
          <span>Model: {widget.basic.aiModel}</span>
        </div>
        <div className="mt-2 flex items-center gap-2">
          {getStatusBadge(widget)}
          <Badge variant="outline">{widget.basic.aiProvider}</Badge>
        </div>
      </CardContent>
      <CardFooter className="pt-3 border-t">
        <div className="flex items-center justify-between w-full text-xs text-muted-foreground">
          <span>
            Created{" "}
            {formatDistanceToNow(new Date(widget.createdAt), {
              addSuffix: true,
            })}
          </span>
          <span>
            Updated{" "}
            {formatDistanceToNow(new Date(widget.updatedAt), {
              addSuffix: true,
            })}
          </span>
        </div>
      </CardFooter>
    </Card>
  );

  if (error) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-destructive mb-4">{error}</p>
          <Button onClick={() => loadWidgets()}>Retry</Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Widgets</h1>
          <p className="text-muted-foreground">
            Manage your AI-powered chat widgets
          </p>
        </div>
        <Button onClick={() => navigate("/admin/widgets/new")}>
          <Plus className="h-4 w-4 mr-2" />
          Create Widget
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-4 flex-1">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search widgets..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                {filterStatus === "all"
                  ? "All"
                  : filterStatus === "published"
                    ? "Published"
                    : "Draft"}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                All Widgets
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("published")}>
                Published Only
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("draft")}>
                Drafts Only
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("grid")}
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === "list" ? "default" : "outline"}
            size="sm"
            onClick={() => setViewMode("list")}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Widget List */}
      {isLoading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading widgets...</p>
          </div>
        </div>
      ) : filteredWidgets.length === 0 ? (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-muted rounded-full flex items-center justify-center mb-4">
            <Plus className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">
            {searchQuery || filterStatus !== "all"
              ? "No widgets found"
              : "No widgets yet"}
          </h3>
          <p className="text-muted-foreground mb-4">
            {searchQuery || filterStatus !== "all"
              ? "Try adjusting your search or filter criteria"
              : "Create your first AI-powered chat widget to get started"}
          </p>
          {!searchQuery && filterStatus === "all" && (
            <Button onClick={() => navigate("/admin/widgets/new")}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Widget
            </Button>
          )}
        </div>
      ) : (
        <div
          className={
            viewMode === "grid"
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
          }
        >
          {filteredWidgets.map((widget) => (
            <WidgetCard key={widget.id} widget={widget} />
          ))}
        </div>
      )}
    </div>
  );
};

export default WidgetList;

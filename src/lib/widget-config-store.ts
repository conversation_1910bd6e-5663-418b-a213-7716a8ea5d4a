import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import {
  WidgetConfig,
  defaultWidgetConfig,
  validateWidgetConfig,
} from "@/types/widget-config";
import { widgetAPI } from "@/lib/api";

interface WidgetConfigState {
  // State
  config: WidgetConfig;
  widgets: WidgetConfig[];
  isDirty: boolean;
  isLoading: boolean;
  error: string | null;
  previewDevice: "desktop" | "tablet" | "mobile";
  previewScale: number;
  selectedWidgetId: string | null;

  // Actions
  loadWidget: (id: string) => Promise<void>;
  loadWidgets: () => Promise<void>;
  createWidget: (config: Omit<WidgetConfig, "id">) => Promise<WidgetConfig>;
  updateConfig: (updates: Partial<WidgetConfig>) => void;
  updateConfigSection: (
    section: keyof WidgetConfig,
    key: string,
    value: any,
  ) => void;
  saveCurrentConfig: () => Promise<void>;
  publishWidget: (widgetId: string) => Promise<WidgetConfig>;
  unpublishWidget: (widgetId: string) => Promise<WidgetConfig>;
  deleteWidget: (widgetId: string) => Promise<void>;
  duplicateWidget: (widgetId: string) => Promise<WidgetConfig>;
  validateCurrentConfig: () => { isValid: boolean; errors: string[] };
  exportConfig: (format: "json" | "yaml") => Promise<Blob>;
  importConfig: (file: File) => Promise<void>;
  resetConfig: () => void;
  setPreviewDevice: (device: "desktop" | "tablet" | "mobile") => void;
  setPreviewScale: (scale: number) => void;
  setSelectedWidget: (id: string | null) => void;
  clearError: () => void;
}

// Validation helper
const validateConfig = (
  config: WidgetConfig,
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // Basic validation
  if (!config.basic.name?.trim()) {
    errors.push("Widget name is required");
  }

  if (!config.basic.aiProvider) {
    errors.push("AI provider is required");
  }

  if (!config.basic.aiModel) {
    errors.push("AI model is required");
  }

  // Styling validation
  if (
    !config.styling.primaryColor?.match(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
  ) {
    errors.push("Invalid primary color format");
  }

  if (
    !config.styling.secondaryColor?.match(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/)
  ) {
    errors.push("Invalid secondary color format");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

export const useWidgetConfigStore = create<WidgetConfigState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        config: defaultWidgetConfig,
        widgets: [],
        isDirty: false,
        isLoading: false,
        error: null,
        previewDevice: "desktop",
        previewScale: 1,
        selectedWidgetId: null,

        // Actions
        loadWidget: async (id: string) => {
          set({ isLoading: true, error: null });
          try {
            const widget = await widgetAPI.getWidget(id);
            set({
              config: widget,
              selectedWidgetId: id,
              isDirty: false,
              isLoading: false,
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to load widget",
              isLoading: false,
            });
            throw error;
          }
        },

        loadWidgets: async () => {
          set({ isLoading: true, error: null });
          try {
            const widgets = await widgetAPI.getWidgets();
            set({
              widgets,
              isLoading: false,
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to load widgets",
              isLoading: false,
            });
            throw error;
          }
        },

        createWidget: async (configData: Omit<WidgetConfig, "id">) => {
          set({ isLoading: true, error: null });
          try {
            // Create a complete widget config with defaults
            const completeConfig = {
              ...defaultWidgetConfig,
              ...configData,
              createdAt: new Date(),
              updatedAt: new Date(),
            };

            const validation = validateConfig(completeConfig);
            if (!validation.isValid) {
              throw new Error(
                `Validation failed: ${validation.errors.join(", ")}`,
              );
            }

            // Call real API to create widget
            const newWidget = await widgetAPI.createWidget(completeConfig);

            set((state) => ({
              config: newWidget,
              widgets: [...state.widgets, newWidget],
              selectedWidgetId: newWidget.id,
              isDirty: false,
              isLoading: false,
            }));
            return newWidget;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to create widget",
              isLoading: false,
            });
            throw error;
          }
        },

        updateConfig: (updates) => {
          set((state) => ({
            config: { ...state.config, ...updates, updatedAt: new Date() },
            isDirty: true,
            error: null,
          }));
        },

        updateConfigSection: (section, key, value) => {
          set((state) => ({
            config: {
              ...state.config,
              [section]: {
                ...(state.config[section] as Record<string, any>),
                [key]: value,
              },
              updatedAt: new Date(),
            },
            isDirty: true,
            error: null,
          }));
        },

        saveCurrentConfig: async () => {
          const { config } = get();
          set({ isLoading: true, error: null });

          try {
            const validation = validateConfig(config);
            if (!validation.isValid) {
              throw new Error(
                `Validation failed: ${validation.errors.join(", ")}`,
              );
            }

            let savedConfig: WidgetConfig;
            if (config.id) {
              // Update existing widget via API
              savedConfig = await widgetAPI.updateWidget(config.id, config);
            } else {
              // Create new widget via API
              savedConfig = await widgetAPI.createWidget(config);
            }

            set((state) => ({
              config: savedConfig,
              widgets: config.id
                ? state.widgets.map((w) =>
                    w.id === savedConfig.id ? savedConfig : w,
                  )
                : [...state.widgets, savedConfig],
              isDirty: false,
              isLoading: false,
            }));
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to save configuration",
              isLoading: false,
            });
            throw error;
          }
        },

        publishWidget: async (widgetId: string) => {
          set({ isLoading: true, error: null });

          try {
            const publishedWidget = get().widgets.find(
              (w) => w.id === widgetId,
            );
            if (!publishedWidget) {
              throw new Error("Widget not found");
            }

            const validation = validateConfig(publishedWidget);
            if (!validation.isValid) {
              throw new Error(
                `Validation failed: ${validation.errors.join(", ")}`,
              );
            }

            // Call real API to publish widget
            const updatedWidget = await widgetAPI.publishWidget(widgetId);

            set((state) => ({
              config:
                state.config.id === widgetId ? updatedWidget : state.config,
              widgets: state.widgets.map((w) =>
                w.id === widgetId ? updatedWidget : w,
              ),
              isDirty: false,
              isLoading: false,
            }));

            return updatedWidget;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to publish widget",
              isLoading: false,
            });
            throw error;
          }
        },

        unpublishWidget: async (widgetId: string) => {
          set({ isLoading: true, error: null });
          try {
            const widget = get().widgets.find((w) => w.id === widgetId);
            if (!widget) {
              throw new Error("Widget not found");
            }

            // Call real API to unpublish widget
            const unpublishedWidget = await widgetAPI.unpublishWidget(widgetId);

            set((state) => ({
              config:
                state.config.id === widgetId ? unpublishedWidget : state.config,
              widgets: state.widgets.map((w) =>
                w.id === widgetId ? unpublishedWidget : w,
              ),
              isLoading: false,
            }));
            return unpublishedWidget;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to unpublish widget",
              isLoading: false,
            });
            throw error;
          }
        },

        deleteWidget: async (widgetId: string) => {
          set({ isLoading: true, error: null });
          try {
            // Call real API to delete widget
            await widgetAPI.deleteWidget(widgetId);

            set((state) => ({
              widgets: state.widgets.filter((w) => w.id !== widgetId),
              config:
                state.config.id === widgetId
                  ? defaultWidgetConfig
                  : state.config,
              selectedWidgetId:
                state.selectedWidgetId === widgetId
                  ? null
                  : state.selectedWidgetId,
              isLoading: false,
            }));
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to delete widget",
              isLoading: false,
            });
            throw error;
          }
        },

        duplicateWidget: async (widgetId: string) => {
          set({ isLoading: true, error: null });
          try {
            const originalWidget = get().widgets.find((w) => w.id === widgetId);
            if (!originalWidget) {
              throw new Error("Widget not found");
            }

            // Call real API to duplicate widget
            const duplicatedWidget = await widgetAPI.duplicateWidget(widgetId);

            set((state) => ({
              widgets: [...state.widgets, duplicatedWidget],
              config: duplicatedWidget,
              selectedWidgetId: duplicatedWidget.id,
              isDirty: false,
              isLoading: false,
            }));
            return duplicatedWidget;
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to duplicate widget",
              isLoading: false,
            });
            throw error;
          }
        },

        validateCurrentConfig: () => {
          const { config } = get();
          return validateConfig(config);
        },

        exportConfig: async (format: "json" | "yaml") => {
          const { config } = get();

          if (!config.id) {
            throw new Error("Cannot export unsaved widget");
          }

          // Call real API to export widget configuration
          return await exportImportAPI.exportWidget(config.id, format);
        },

        importConfig: async (file: File) => {
          set({ isLoading: true, error: null });

          try {
            // Call real API to import widget configuration
            const importedWidget = await exportImportAPI.importWidget(file);
            const validatedConfig = validateWidgetConfig(importedWidget);

            set({
              config: {
                ...validatedConfig,
                id: undefined,
                createdAt: new Date(),
                updatedAt: new Date(),
              },
              isDirty: true,
              isLoading: false,
            });
          } catch (error) {
            set({
              error:
                error instanceof Error
                  ? error.message
                  : "Failed to import configuration",
              isLoading: false,
            });
            throw error;
          }
        },

        resetConfig: () => {
          set({
            config: defaultWidgetConfig,
            isDirty: false,
            error: null,
            selectedWidgetId: null,
          });
        },

        setPreviewDevice: (device) => {
          set({ previewDevice: device });
        },

        setPreviewScale: (scale) => {
          set({ previewScale: Math.max(0.25, Math.min(2, scale)) });
        },

        setSelectedWidget: (id) => {
          set({ selectedWidgetId: id });
        },

        clearError: () => {
          set({ error: null });
        },
      }),
      {
        name: "widget-config-store",
        partialize: (state) => ({
          config: state.config,
          previewDevice: state.previewDevice,
          previewScale: state.previewScale,
        }),
      },
    ),
    {
      name: "widget-config-store",
    },
  ),
);

// Hook exports for backward compatibility
export const useCurrentConfig = () =>
  useWidgetConfigStore((state) => state.config);
export const useWidgets = () => useWidgetConfigStore((state) => state.widgets);
export const useIsDirty = () => useWidgetConfigStore((state) => state.isDirty);
export const useIsLoading = () =>
  useWidgetConfigStore((state) => state.isLoading);
export const useError = () => useWidgetConfigStore((state) => state.error);
export const useSelectedWidgetId = () =>
  useWidgetConfigStore((state) => state.selectedWidgetId);

export const useWidgetActions = () =>
  useWidgetConfigStore((state) => ({
    loadWidget: state.loadWidget,
    loadWidgets: state.loadWidgets,
    createWidget: state.createWidget,
    updateConfig: state.updateConfig,
    updateConfigSection: state.updateConfigSection,
    saveCurrentConfig: state.saveCurrentConfig,
    publishWidget: state.publishWidget,
    unpublishWidget: state.unpublishWidget,
    deleteWidget: state.deleteWidget,
    duplicateWidget: state.duplicateWidget,
    validateCurrentConfig: state.validateCurrentConfig,
    exportConfig: state.exportConfig,
    importConfig: state.importConfig,
    resetConfig: state.resetConfig,
    setSelectedWidget: state.setSelectedWidget,
    clearError: state.clearError,
  }));

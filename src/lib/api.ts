import {
  WidgetConfig,
  AIModel,
  AIProvider,
  UserAIProviderConfig,
  WidgetAnalytics,
  ConversationTest,
  EmbedCodeOptions,
} from "@/types/widget-config";

// API Configuration
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:8000/api";
const API_VERSION = "v1";

// API Response Types
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
  meta?: {
    pagination?: {
      current_page: number;
      last_page: number;
      per_page: number;
      total: number;
    };
  };
}

// HTTP Client Class
class APIClient {
  private baseURL: string;
  private headers: Record<string, string>;

  constructor() {
    this.baseURL = `${API_BASE_URL}/${API_VERSION}`;
    this.headers = {
      "Content-Type": "application/json",
      Accept: "application/json",
      "X-Requested-With": "XMLHttpRequest",
    };

    // Add CSRF token if available
    const csrfToken = document
      .querySelector('meta[name="csrf-token"]')
      ?.getAttribute("content");
    if (csrfToken) {
      this.headers["X-CSRF-TOKEN"] = csrfToken;
    }

    // Add auth token if available
    const authToken = localStorage.getItem("auth_token");
    if (authToken) {
      this.headers["Authorization"] = `Bearer ${authToken}`;
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.headers,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.message || `HTTP ${response.status}: ${response.statusText}`,
        );
      }

      return data;
    } catch (error) {
      console.error("API Request failed:", error);
      throw error;
    }
  }

  async get<T>(
    endpoint: string,
    params?: Record<string, any>,
  ): Promise<APIResponse<T>> {
    const url = new URL(`${this.baseURL}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    return this.request(url.pathname + url.search);
  }

  async post<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request(endpoint, {
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string): Promise<APIResponse<T>> {
    return this.request(endpoint, {
      method: "DELETE",
    });
  }
}

// Create API client instance
const apiClient = new APIClient();

// Widget API
export const widgetAPI = {
  // CRUD Operations
  async getWidgets(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    status?: "draft" | "published" | "archived";
    sort_by?: string;
    sort_order?: "asc" | "desc";
  }): Promise<WidgetConfig[]> {
    const response = await apiClient.get<WidgetConfig[]>("/widgets", params);
    return response.data || [];
  },

  async getWidget(id: string): Promise<WidgetConfig> {
    const response = await apiClient.get<WidgetConfig>(`/widgets/${id}`);
    if (!response.data) {
      throw new Error("Widget not found");
    }
    return response.data;
  },

  async createWidget(config: Omit<WidgetConfig, "id">): Promise<WidgetConfig> {
    const response = await apiClient.post<WidgetConfig>("/widgets", config);
    if (!response.data) {
      throw new Error("Failed to create widget");
    }
    return response.data;
  },

  async updateWidget(
    id: string,
    config: Partial<WidgetConfig>,
  ): Promise<WidgetConfig> {
    const response = await apiClient.put<WidgetConfig>(
      `/widgets/${id}`,
      config,
    );
    if (!response.data) {
      throw new Error("Failed to update widget");
    }
    return response.data;
  },

  async deleteWidget(id: string): Promise<void> {
    await apiClient.delete(`/widgets/${id}`);
  },

  async duplicateWidget(id: string): Promise<WidgetConfig> {
    const response = await apiClient.post<WidgetConfig>(
      `/widgets/${id}/duplicate`,
    );
    if (!response.data) {
      throw new Error("Failed to duplicate widget");
    }
    return response.data;
  },

  // Publishing
  async publishWidget(id: string): Promise<WidgetConfig> {
    const response = await apiClient.post<WidgetConfig>(
      `/widgets/${id}/publish`,
    );
    if (!response.data) {
      throw new Error("Failed to publish widget");
    }
    return response.data;
  },

  async unpublishWidget(id: string): Promise<WidgetConfig> {
    const response = await apiClient.post<WidgetConfig>(
      `/widgets/${id}/unpublish`,
    );
    if (!response.data) {
      throw new Error("Failed to unpublish widget");
    }
    return response.data;
  },

  // Embed Code Generation
  async generateEmbedCode(
    id: string,
    options: EmbedCodeOptions,
  ): Promise<string> {
    const response = await apiClient.post<{ code: string }>(
      `/widgets/${id}/embed`,
      options,
    );
    return response.data?.code || "";
  },

  async getEmbedCode(
    id: string,
    type: "iframe" | "script" | "react" | "vue" | "wordpress",
  ): Promise<string> {
    const response = await apiClient.get<{ code: string }>(
      `/widgets/${id}/embed/${type}`,
    );
    return response.data?.code || "";
  },

  // Preview
  async getPreviewUrl(id: string): Promise<string> {
    const response = await apiClient.get<{ url: string }>(
      `/widgets/${id}/preview`,
    );
    return response.data?.url || "";
  },

  // Analytics
  async getWidgetAnalytics(
    id: string,
    params?: {
      start_date?: string;
      end_date?: string;
      granularity?: "hour" | "day" | "week" | "month";
    },
  ): Promise<WidgetAnalytics> {
    const response = await apiClient.get<WidgetAnalytics>(
      `/widgets/${id}/analytics`,
      params,
    );
    if (!response.data) {
      throw new Error("Failed to fetch analytics");
    }
    return response.data;
  },
};

// AI Provider API
export const aiProviderAPI = {
  async getProviders(): Promise<AIProvider[]> {
    const response = await apiClient.get<AIProvider[]>("/ai-providers");
    return response.data || [];
  },

  async getProvider(id: string): Promise<AIProvider> {
    const response = await apiClient.get<AIProvider>(`/ai-providers/${id}`);
    if (!response.data) {
      throw new Error("Provider not found");
    }
    return response.data;
  },

  async createProvider(data: {
    name: string;
    slug: string;
    api_key: string;
    settings?: Record<string, any>;
  }): Promise<AIProvider> {
    const response = await apiClient.post<AIProvider>("/ai-providers", data);
    if (!response.data) {
      throw new Error("Failed to create provider");
    }
    return response.data;
  },

  async updateProvider(
    id: string,
    data: {
      api_key?: string;
      settings?: Record<string, any>;
      is_active?: boolean;
    },
  ): Promise<AIProvider> {
    const response = await apiClient.put<AIProvider>(
      `/ai-providers/${id}`,
      data,
    );
    if (!response.data) {
      throw new Error("Failed to update provider");
    }
    return response.data;
  },

  async deleteProvider(id: string): Promise<void> {
    await apiClient.delete(`/ai-providers/${id}`);
  },

  async testProvider(
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post<{
      success: boolean;
      message: string;
    }>(`/ai-providers/${id}/test`);
    return response.data || { success: false, message: "Test failed" };
  },

  async fetchModels(providerId: string): Promise<AIModel[]> {
    const response = await apiClient.post<AIModel[]>(
      `/ai-providers/${providerId}/fetch-models`,
    );
    return response.data || [];
  },

  async getAvailableModels(providerId?: string): Promise<AIModel[]> {
    const params = providerId ? { provider_id: providerId } : undefined;
    const response = await apiClient.get<AIModel[]>("/ai-models", params);
    return response.data || [];
  },

  async enableModel(modelId: string): Promise<AIModel> {
    const response = await apiClient.post<AIModel>(
      `/ai-models/${modelId}/enable`,
    );
    if (!response.data) {
      throw new Error("Failed to enable model");
    }
    return response.data;
  },

  async disableModel(modelId: string): Promise<AIModel> {
    const response = await apiClient.post<AIModel>(
      `/ai-models/${modelId}/disable`,
    );
    if (!response.data) {
      throw new Error("Failed to disable model");
    }
    return response.data;
  },

  async getUserProviderConfigs(): Promise<UserAIProviderConfig[]> {
    const response = await apiClient.get<UserAIProviderConfig[]>(
      "/user/ai-provider-configs",
    );
    return response.data || [];
  },

  async createUserProviderConfig(data: {
    ai_provider_id: string;
    api_key: string;
    settings?: Record<string, any>;
  }): Promise<UserAIProviderConfig> {
    const response = await apiClient.post<UserAIProviderConfig>(
      "/user/ai-provider-configs",
      data,
    );
    if (!response.data) {
      throw new Error("Failed to create provider configuration");
    }
    return response.data;
  },

  async updateUserProviderConfig(
    id: string,
    data: {
      api_key?: string;
      settings?: Record<string, any>;
      is_active?: boolean;
    },
  ): Promise<UserAIProviderConfig> {
    const response = await apiClient.put<UserAIProviderConfig>(
      `/user/ai-provider-configs/${id}`,
      data,
    );
    if (!response.data) {
      throw new Error("Failed to update provider configuration");
    }
    return response.data;
  },

  async deleteUserProviderConfig(id: string): Promise<void> {
    await apiClient.delete(`/user/ai-provider-configs/${id}`);
  },
};

// Testing API
export const testingAPI = {
  async getConversationTests(widgetId: string): Promise<ConversationTest[]> {
    const response = await apiClient.get<ConversationTest[]>(
      `/widgets/${widgetId}/tests`,
    );
    return response.data || [];
  },

  async createConversationTest(
    widgetId: string,
    test: Omit<ConversationTest, "id" | "widget_id" | "status">,
  ): Promise<ConversationTest> {
    const response = await apiClient.post<ConversationTest>(
      `/widgets/${widgetId}/tests`,
      test,
    );
    if (!response.data) {
      throw new Error("Failed to create test");
    }
    return response.data;
  },

  async runConversationTest(testId: string): Promise<ConversationTest> {
    const response = await apiClient.post<ConversationTest>(
      `/tests/${testId}/run`,
    );
    if (!response.data) {
      throw new Error("Failed to run test");
    }
    return response.data;
  },

  async runAllTests(widgetId: string): Promise<ConversationTest[]> {
    const response = await apiClient.post<ConversationTest[]>(
      `/widgets/${widgetId}/tests/run-all`,
    );
    return response.data || [];
  },

  async simulateConversation(
    widgetId: string,
    messages: Array<{ role: "user" | "assistant"; content: string }>,
  ): Promise<{
    responses: Array<{ content: string; timestamp: number }>;
    performance: {
      avg_response_time: number;
      total_time: number;
    };
  }> {
    const response = await apiClient.post(`/widgets/${widgetId}/simulate`, {
      messages,
    });
    return (
      response.data || {
        responses: [],
        performance: { avg_response_time: 0, total_time: 0 },
      }
    );
  },
};

// Collaboration API
export const collaborationAPI = {
  async getCollaborators(widgetId: string): Promise<
    Array<{
      id: string;
      name: string;
      email: string;
      avatar?: string;
      role: "owner" | "editor" | "viewer";
      last_seen: string;
      is_active: boolean;
    }>
  > {
    const response = await apiClient.get(`/widgets/${widgetId}/collaborators`);
    return response.data || [];
  },

  async inviteCollaborator(
    widgetId: string,
    email: string,
    role: "editor" | "viewer",
  ): Promise<void> {
    await apiClient.post(`/widgets/${widgetId}/collaborators`, { email, role });
  },

  async removeCollaborator(widgetId: string, userId: string): Promise<void> {
    await apiClient.delete(`/widgets/${widgetId}/collaborators/${userId}`);
  },

  async updateCollaboratorRole(
    widgetId: string,
    userId: string,
    role: "editor" | "viewer",
  ): Promise<void> {
    await apiClient.put(`/widgets/${widgetId}/collaborators/${userId}`, {
      role,
    });
  },
};

// Export/Import API
export const exportImportAPI = {
  async exportWidget(id: string, format: "json" | "yaml"): Promise<Blob> {
    const response = await fetch(
      `${apiClient["baseURL"]}/widgets/${id}/export?format=${format}`,
      {
        headers: apiClient["headers"],
      },
    );

    if (!response.ok) {
      throw new Error("Export failed");
    }

    return response.blob();
  },

  async importWidget(file: File): Promise<WidgetConfig> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await fetch(`${apiClient["baseURL"]}/widgets/import`, {
      method: "POST",
      headers: {
        ...apiClient["headers"],
        "Content-Type": undefined, // Let browser set content-type for FormData
      },
      body: formData,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || "Import failed");
    }

    return data.data;
  },

  async exportMultipleWidgets(
    ids: string[],
    format: "json" | "yaml",
  ): Promise<Blob> {
    const response = await fetch(
      `${apiClient["baseURL"]}/widgets/export-multiple`,
      {
        method: "POST",
        headers: apiClient["headers"],
        body: JSON.stringify({ widget_ids: ids, format }),
      },
    );

    if (!response.ok) {
      throw new Error("Bulk export failed");
    }

    return response.blob();
  },
};

// Utility functions
export const apiUtils = {
  // Handle API errors consistently
  handleError: (error: any): string => {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    if (error.message) {
      return error.message;
    }
    return "An unexpected error occurred";
  },

  // Format validation errors
  formatValidationErrors: (errors: Record<string, string[]>): string => {
    return Object.entries(errors)
      .map(([field, messages]) => `${field}: ${messages.join(", ")}`)
      .join("; ");
  },

  // Check if user is online
  isOnline: (): boolean => navigator.onLine,

  // Retry failed requests
  retry: async <T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
  ): Promise<T> => {
    let lastError: any;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        if (i < maxRetries) {
          await new Promise((resolve) =>
            setTimeout(resolve, delay * Math.pow(2, i)),
          );
        }
      }
    }

    throw lastError;
  },
};

// Authentication API
export const authAPI = {
  async login(credentials: {
    email: string;
    password: string;
    remember_me?: boolean;
  }): Promise<{
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
      avatar?: string;
      preferences?: Record<string, any>;
      timezone: string;
    };
    token: string;
    expires_at: string;
  }> {
    const response = await apiClient.post<{
      user: {
        id: string;
        name: string;
        email: string;
        role: string;
        avatar?: string;
        preferences?: Record<string, any>;
        timezone: string;
      };
      token: string;
      expires_at: string;
    }>("/auth/login", credentials);

    if (!response.data) {
      throw new Error("Login failed");
    }

    // Store token in localStorage
    localStorage.setItem("auth_token", response.data.token);
    localStorage.setItem("user", JSON.stringify(response.data.user));

    return response.data;
  },

  async quickAdminLogin(): Promise<{
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
      avatar?: string;
      preferences?: Record<string, any>;
      timezone: string;
    };
    token: string;
    expires_at: string;
  }> {
    const response = await apiClient.post<{
      user: {
        id: string;
        name: string;
        email: string;
        role: string;
        avatar?: string;
        preferences?: Record<string, any>;
        timezone: string;
      };
      token: string;
      expires_at: string;
    }>("/auth/quick-admin-login");

    if (!response.data) {
      throw new Error("Quick admin login failed");
    }

    // Store token in localStorage
    localStorage.setItem("auth_token", response.data.token);
    localStorage.setItem("user", JSON.stringify(response.data.user));

    return response.data;
  },

  async register(userData: {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    timezone?: string;
  }): Promise<{
    user: {
      id: string;
      name: string;
      email: string;
      role: string;
      avatar?: string;
      preferences?: Record<string, any>;
      timezone: string;
    };
    token: string;
    expires_at: string;
  }> {
    const response = await apiClient.post<{
      user: {
        id: string;
        name: string;
        email: string;
        role: string;
        avatar?: string;
        preferences?: Record<string, any>;
        timezone: string;
      };
      token: string;
      expires_at: string;
    }>("/auth/register", userData);

    if (!response.data) {
      throw new Error("Registration failed");
    }

    // Store token in localStorage
    localStorage.setItem("auth_token", response.data.token);
    localStorage.setItem("user", JSON.stringify(response.data.user));

    return response.data;
  },

  async logout(): Promise<void> {
    try {
      await apiClient.post("/auth/logout");
    } finally {
      // Always clear local storage
      localStorage.removeItem("auth_token");
      localStorage.removeItem("user");
    }
  },

  async me(): Promise<{
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
    preferences?: Record<string, any>;
    timezone: string;
    last_login_at?: string;
  }> {
    const response = await apiClient.get<{
      user: {
        id: string;
        name: string;
        email: string;
        role: string;
        avatar?: string;
        preferences?: Record<string, any>;
        timezone: string;
        last_login_at?: string;
      };
    }>("/auth/me");

    if (!response.data?.user) {
      throw new Error("Failed to get user data");
    }

    return response.data.user;
  },

  async refreshToken(): Promise<{
    token: string;
    expires_at: string;
  }> {
    const response = await apiClient.post<{
      token: string;
      expires_at: string;
    }>("/auth/refresh");

    if (!response.data) {
      throw new Error("Token refresh failed");
    }

    // Update token in localStorage
    localStorage.setItem("auth_token", response.data.token);

    return response.data;
  },

  async revokeAllTokens(): Promise<void> {
    await apiClient.post("/auth/revoke-all");
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user");
  },

  // Utility functions
  isAuthenticated(): boolean {
    return !!localStorage.getItem("auth_token");
  },

  getCurrentUser(): {
    id: string;
    name: string;
    email: string;
    role: string;
    avatar?: string;
    preferences?: Record<string, any>;
    timezone: string;
  } | null {
    const userStr = localStorage.getItem("user");
    return userStr ? JSON.parse(userStr) : null;
  },

  getToken(): string | null {
    return localStorage.getItem("auth_token");
  },

  isAdmin(): boolean {
    const user = this.getCurrentUser();
    return user?.role === "admin";
  },
};

// Export the API client for direct use if needed
export { apiClient };

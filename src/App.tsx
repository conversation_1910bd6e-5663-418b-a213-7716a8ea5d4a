import React, { Suspense, useState } from "react";
import { useRoutes, Routes, Route, Navigate } from "react-router-dom";
import Home from "./components/home";
import LandingPage from "./components/LandingPage";
import AdminDashboard from "./components/admin/AdminDashboard";
import WidgetConfigurator from "./components/admin/WidgetConfigurator";
import WidgetList from "./components/admin/WidgetList";
import AIProviderManager from "./components/admin/AIProviderManager";
import MainLayout from "./components/layout/MainLayout";
import AdminLayout from "./components/layout/AdminLayout";
import Login from "./components/auth/Login";
import SignUp from "./components/auth/SignUp";
import { Toaster } from "./components/ui/toaster";
import routes from "tempo-routes";
import { Loader2 } from "lucide-react";

// Loading component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center min-h-screen">
    <Loader2 className="h-8 w-8 animate-spin" />
  </div>
);

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Application Error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Something went wrong</h2>
            <p className="text-muted-foreground mb-4">
              {this.state.error?.message || "An unexpected error occurred"}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

function App() {
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
    document.documentElement.classList.toggle("dark");
  };

  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingSpinner />}>
        <div className={isDarkMode ? "dark" : ""}>
          <Routes>
            {/* Public Routes with MainLayout */}
            <Route
              path="/"
              element={
                <MainLayout
                  isDarkMode={isDarkMode}
                  toggleTheme={toggleTheme}
                  hideFooter={true}
                >
                  <LandingPage />
                </MainLayout>
              }
            />

            {/* Admin Routes */}
            <Route
              path="/admin"
              element={<Navigate to="/admin/dashboard" replace />}
            />
            <Route
              path="/admin/dashboard"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <AdminDashboard />
                </Suspense>
              }
            />
            <Route
              path="/admin/widgets"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <AdminLayout>
                    <WidgetList />
                  </AdminLayout>
                </Suspense>
              }
            />
            <Route
              path="/admin/widgets/new"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <WidgetConfigurator />
                </Suspense>
              }
            />
            <Route
              path="/admin/widgets/:id"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <WidgetConfigurator />
                </Suspense>
              }
            />
            <Route
              path="/admin/providers"
              element={
                <Suspense fallback={<LoadingSpinner />}>
                  <AdminLayout>
                    <AIProviderManager />
                  </AdminLayout>
                </Suspense>
              }
            />

            {/* Auth Routes */}
            <Route
              path="/login"
              element={
                <Login isDarkMode={isDarkMode} toggleTheme={toggleTheme} />
              }
            />
            <Route
              path="/signup"
              element={
                <SignUp isDarkMode={isDarkMode} toggleTheme={toggleTheme} />
              }
            />

            {/* Other public routes */}
            <Route
              path="/features"
              element={
                <MainLayout isDarkMode={isDarkMode} toggleTheme={toggleTheme}>
                  <div className="container py-24">
                    <div className="text-center">
                      <h1 className="text-3xl font-bold mb-4">Features</h1>
                      <p className="text-muted-foreground">
                        Features page coming soon...
                      </p>
                    </div>
                  </div>
                </MainLayout>
              }
            />
            <Route
              path="/pricing"
              element={
                <MainLayout isDarkMode={isDarkMode} toggleTheme={toggleTheme}>
                  <div className="container py-24">
                    <div className="text-center">
                      <h1 className="text-3xl font-bold mb-4">Pricing</h1>
                      <p className="text-muted-foreground">
                        Pricing page coming soon...
                      </p>
                    </div>
                  </div>
                </MainLayout>
              }
            />
            <Route
              path="/documentation"
              element={
                <MainLayout isDarkMode={isDarkMode} toggleTheme={toggleTheme}>
                  <div className="container py-24">
                    <div className="text-center">
                      <h1 className="text-3xl font-bold mb-4">Documentation</h1>
                      <p className="text-muted-foreground">
                        Documentation page coming soon...
                      </p>
                    </div>
                  </div>
                </MainLayout>
              }
            />

            {/* 404 Route */}
            <Route
              path=""
              element={
                <MainLayout isDarkMode={isDarkMode} toggleTheme={toggleTheme}>
                  <div className="container py-24">
                    <div className="text-center">
                      <h1 className="text-4xl font-bold mb-4">404</h1>
                      <p className="text-muted-foreground mb-8">
                        Page not found
                      </p>
                      <a href="/" className="text-primary hover:underline">
                        Go back home
                      </a>
                    </div>
                  </div>
                </MainLayout>
              }
            />
          </Routes>

          {/* Tempo Routes - Disabled to prevent Router nesting */}
          {/* {import.meta.env.VITE_TEMPO === "true" && useRoutes(routes)} */}

          {/* Toast notifications */}
          <Toaster />
        </div>
      </Suspense>
    </ErrorBoundary>
  );
}

export default App;

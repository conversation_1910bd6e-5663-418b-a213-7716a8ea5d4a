import { z } from "zod";

// Color validation schema
const colorSchema = z
  .string()
  .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Invalid color format");

// Font family enum
const fontFamilySchema = z.enum([
  "Inter",
  "Roboto",
  "Open Sans",
  "Lato",
  "Poppins",
  "Montserrat",
  "Source Sans Pro",
  "Nunito",
  "Raleway",
  "Ubuntu",
]);

// Widget position enum
const positionSchema = z.enum([
  "bottom-right",
  "bottom-left",
  "top-right",
  "top-left",
]);

// Icon style enum
const iconStyleSchema = z.enum([
  "chat",
  "message",
  "question",
  "support",
  "help",
]);

// Animation type enum
const animationTypeSchema = z.enum([
  "none",
  "fade",
  "slide",
  "bounce",
  "pulse",
  "shake",
]);

// AI Provider enum
const aiProviderSchema = z.enum([
  "openai",
  "groq",
  "gemini",
  "mistral",
  "deepseek",
  "huggingface",
  "openrouter",
]);

// Device type enum
const deviceTypeSchema = z.enum(["desktop", "tablet", "mobile"]);

// Basic Settings Schema
const basicSettingsSchema = z.object({
  name: z.string().min(1, "Widget name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  isActive: z.boolean().default(true),
  aiProvider: aiProviderSchema,
  aiModel: z.string().min(1, "AI model is required"),
  systemPrompt: z
    .string()
    .max(2000, "System prompt too long")
    .default("You are a helpful customer support assistant."),
  temperature: z.number().min(0).max(2).default(0.7),
  maxTokens: z.number().min(1).max(4000).default(1000),
  responseTimeout: z.number().min(1000).max(30000).default(10000),
});

// Styling Options Schema
const stylingSchema = z.object({
  primaryColor: colorSchema.default("#4f46e5"),
  secondaryColor: colorSchema.default("#ffffff"),
  backgroundColor: colorSchema.default("#f8fafc"),
  textColor: colorSchema.default("#1f2937"),
  accentColor: colorSchema.default("#10b981"),
  errorColor: colorSchema.default("#ef4444"),
  fontFamily: fontFamilySchema.default("Inter"),
  fontSize: z.number().min(12).max(20).default(14),
  fontWeight: z
    .enum(["normal", "medium", "semibold", "bold"])
    .default("normal"),
  borderRadius: z.number().min(0).max(50).default(8),
  shadowIntensity: z.enum(["none", "sm", "md", "lg", "xl"]).default("md"),
  animation: animationTypeSchema.default("fade"),
  customCSS: z.string().max(5000).optional(),
});

// Behavior Settings Schema
const behaviorSchema = z.object({
  autoOpen: z.boolean().default(false),
  autoOpenDelay: z.number().min(0).max(60000).default(3000),
  autoOpenTrigger: z
    .enum(["time", "scroll", "exit-intent", "page-visit"])
    .default("time"),
  scrollTriggerPercent: z.number().min(0).max(100).default(50),
  welcomeMessage: z
    .string()
    .max(500)
    .default("Hello! How can I help you today?"),
  placeholderText: z.string().max(100).default("Type your message here..."),
  showTypingIndicator: z.boolean().default(true),
  typingIndicatorDelay: z.number().min(500).max(5000).default(1500),
  enableSoundNotifications: z.boolean().default(false),
  soundVolume: z.number().min(0).max(1).default(0.5),
  showBranding: z.boolean().default(true),
  enableFileUpload: z.boolean().default(false),
  maxFileSize: z.number().min(1).max(10).default(5), // MB
  allowedFileTypes: z
    .array(z.string())
    .default(["image/jpeg", "image/png", "application/pdf"]),
  enableEmojis: z.boolean().default(true),
  enableMarkdown: z.boolean().default(false),
  conversationStarters: z.array(z.string().max(100)).max(5).default([]),
});

// Positioning & Size Schema
const positioningSchema = z.object({
  position: positionSchema.default("bottom-right"),
  offsetX: z.number().min(0).max(200).default(20),
  offsetY: z.number().min(0).max(200).default(20),
  width: z.number().min(300).max(600).default(350),
  height: z.number().min(400).max(800).default(600),
  minWidth: z.number().min(250).max(400).default(300),
  minHeight: z.number().min(300).max(500).default(400),
  maxWidth: z.number().min(400).max(1000).default(500),
  maxHeight: z.number().min(500).max(1200).default(700),
  zIndex: z.number().min(1000).max(9999).default(9999),
  iconStyle: iconStyleSchema.default("chat"),
  iconSize: z.number().min(40).max(80).default(60),
  mobileBreakpoint: z.number().min(320).max(768).default(768),
  tabletBreakpoint: z.number().min(768).max(1024).default(1024),
  responsiveScaling: z.boolean().default(true),
});

// Advanced Features Schema
const advancedSchema = z.object({
  allowedDomains: z.array(z.string().url()).default([]),
  blockedDomains: z.array(z.string().url()).default([]),
  rateLimitPerMinute: z.number().min(1).max(100).default(20),
  rateLimitPerHour: z.number().min(10).max(1000).default(200),
  enableAnalytics: z.boolean().default(true),
  analyticsProvider: z
    .enum(["google", "mixpanel", "amplitude", "custom"])
    .default("google"),
  customAnalyticsCode: z.string().max(2000).optional(),
  enableA11y: z.boolean().default(true),
  keyboardNavigation: z.boolean().default(true),
  screenReaderSupport: z.boolean().default(true),
  highContrastMode: z.boolean().default(false),
  enableGDPR: z.boolean().default(false),
  gdprMessage: z.string().max(500).optional(),
  cookieConsent: z.boolean().default(false),
  dataRetentionDays: z.number().min(1).max(365).default(30),
  enableEncryption: z.boolean().default(true),
  webhookUrl: z.string().url().optional(),
  webhookEvents: z
    .array(
      z.enum(["message_sent", "conversation_started", "conversation_ended"]),
    )
    .default([]),
  customHeaders: z.record(z.string()).default({}),
  enableCORS: z.boolean().default(true),
  corsOrigins: z.array(z.string()).default(["*"]),
});

// Device Preview Schema
const devicePreviewSchema = z.object({
  currentDevice: deviceTypeSchema.default("desktop"),
  customWidth: z.number().min(320).max(2560).optional(),
  customHeight: z.number().min(568).max(1440).optional(),
  orientation: z.enum(["portrait", "landscape"]).default("portrait"),
  showDeviceFrame: z.boolean().default(true),
  deviceScale: z.number().min(0.25).max(2).default(1),
});

// Testing Configuration Schema
const testingSchema = z.object({
  enableTesting: z.boolean().default(false),
  testScenarios: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        messages: z.array(
          z.object({
            role: z.enum(["user", "assistant"]),
            content: z.string(),
            timestamp: z.number(),
          }),
        ),
      }),
    )
    .default([]),
  performanceMonitoring: z.boolean().default(true),
  errorTracking: z.boolean().default(true),
  loadTesting: z.boolean().default(false),
  maxConcurrentUsers: z.number().min(1).max(1000).default(100),
});

// Main Widget Configuration Schema
export const widgetConfigSchema = z.object({
  id: z.string().uuid().optional(),
  version: z.string().default("1.0.0"),
  createdAt: z.date().default(() => new Date()),
  updatedAt: z.date().default(() => new Date()),
  createdBy: z.string().optional(),
  lastModifiedBy: z.string().optional(),
  basic: basicSettingsSchema,
  styling: stylingSchema,
  behavior: behaviorSchema,
  positioning: positioningSchema,
  advanced: advancedSchema,
  devicePreview: devicePreviewSchema,
  testing: testingSchema,
  isDraft: z.boolean().default(true),
  isPublished: z.boolean().default(false),
  publishedAt: z.date().optional(),
  tags: z.array(z.string()).default([]),
  category: z.string().optional(),
  priority: z.enum(["low", "medium", "high", "critical"]).default("medium"),
});

// AI Provider and Model Types
export interface AIModel {
  id: string;
  name: string;
  provider: string;
  description?: string;
  max_tokens: number;
  supports_streaming: boolean;
  cost_per_token?: number;
  is_available: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface AIProvider {
  id: string;
  name: string;
  slug: string;
  description?: string;
  api_key_required: boolean;
  is_configured: boolean;
  models: AIModel[];
  status: "active" | "inactive" | "error";
  last_tested_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface UserAIProviderConfig {
  id: string;
  user_id: string;
  ai_provider_id: string;
  api_key: string;
  settings?: Record<string, any>;
  is_active: boolean;
  last_tested_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface WidgetAnalytics {
  widget_id: string;
  total_conversations: number;
  total_messages: number;
  avg_response_time: number;
  user_satisfaction: number;
  most_common_queries: Array<{
    query: string;
    count: number;
  }>;
  daily_stats: Array<{
    date: string;
    conversations: number;
    messages: number;
  }>;
}

export interface ConversationTest {
  id: string;
  widget_id: string;
  name: string;
  messages: Array<{
    role: "user" | "assistant";
    content: string;
    timestamp: number;
  }>;
  expected_responses?: Array<{
    message_index: number;
    expected_content: string;
    match_type: "exact" | "contains" | "regex";
  }>;
  status: "pending" | "running" | "completed" | "failed";
  results?: {
    passed: boolean;
    score: number;
    details: string;
  };
  created_at?: string;
  updated_at?: string;
}

export interface EmbedCodeOptions {
  type: "iframe" | "script" | "react" | "vue" | "wordpress";
  widget_id: string;
  base_url: string;
  width?: number;
  height?: number;
  custom_css?: string;
}

// Export TypeScript types
export type WidgetConfig = z.infer<typeof widgetConfigSchema>;
export type BasicSettings = z.infer<typeof basicSettingsSchema>;
export type StylingOptions = z.infer<typeof stylingSchema>;
export type BehaviorSettings = z.infer<typeof behaviorSchema>;
export type PositioningSettings = z.infer<typeof positioningSchema>;
export type AdvancedSettings = z.infer<typeof advancedSchema>;
export type DevicePreview = z.infer<typeof devicePreviewSchema>;
export type TestingConfig = z.infer<typeof testingSchema>;
export type FontFamily = z.infer<typeof fontFamilySchema>;
export type Position = z.infer<typeof positionSchema>;
export type IconStyle = z.infer<typeof iconStyleSchema>;
export type AnimationType = z.infer<typeof animationTypeSchema>;
export type AIProviderType = z.infer<typeof aiProviderSchema>;
export type DeviceType = z.infer<typeof deviceTypeSchema>;

// Validation helpers
export const validateWidgetConfig = (config: unknown): WidgetConfig => {
  return widgetConfigSchema.parse(config);
};

export const validatePartialWidgetConfig = (
  config: unknown,
): Partial<WidgetConfig> => {
  return widgetConfigSchema.partial().parse(config);
};

// Default configuration
export const defaultWidgetConfig: WidgetConfig = {
  version: "1.0.0",
  createdAt: new Date(),
  updatedAt: new Date(),
  basic: {
    name: "New Widget",
    description: "",
    isActive: true,
    aiProvider: "openai",
    aiModel: "gpt-3.5-turbo",
    systemPrompt: "You are a helpful customer support assistant.",
    temperature: 0.7,
    maxTokens: 1000,
    responseTimeout: 10000,
  },
  styling: {
    primaryColor: "#4f46e5",
    secondaryColor: "#ffffff",
    backgroundColor: "#f8fafc",
    textColor: "#1f2937",
    accentColor: "#10b981",
    errorColor: "#ef4444",
    fontFamily: "Inter",
    fontSize: 14,
    fontWeight: "normal",
    borderRadius: 8,
    shadowIntensity: "md",
    animation: "fade",
    customCSS: "",
  },
  behavior: {
    autoOpen: false,
    autoOpenDelay: 3000,
    autoOpenTrigger: "time",
    scrollTriggerPercent: 50,
    welcomeMessage: "Hello! How can I help you today?",
    placeholderText: "Type your message here...",
    showTypingIndicator: true,
    typingIndicatorDelay: 1500,
    enableSoundNotifications: false,
    soundVolume: 0.5,
    showBranding: true,
    enableFileUpload: false,
    maxFileSize: 5,
    allowedFileTypes: ["image/jpeg", "image/png", "application/pdf"],
    enableEmojis: true,
    enableMarkdown: false,
    conversationStarters: [],
  },
  positioning: {
    position: "bottom-right",
    offsetX: 20,
    offsetY: 20,
    width: 350,
    height: 600,
    minWidth: 300,
    minHeight: 400,
    maxWidth: 500,
    maxHeight: 700,
    zIndex: 9999,
    iconStyle: "chat",
    iconSize: 60,
    mobileBreakpoint: 768,
    tabletBreakpoint: 1024,
    responsiveScaling: true,
  },
  advanced: {
    allowedDomains: [],
    blockedDomains: [],
    rateLimitPerMinute: 20,
    rateLimitPerHour: 200,
    enableAnalytics: true,
    analyticsProvider: "google",
    customAnalyticsCode: "",
    enableA11y: true,
    keyboardNavigation: true,
    screenReaderSupport: true,
    highContrastMode: false,
    enableGDPR: false,
    gdprMessage: "",
    cookieConsent: false,
    dataRetentionDays: 30,
    enableEncryption: true,
    webhookUrl: "",
    webhookEvents: [],
    customHeaders: {},
    enableCORS: true,
    corsOrigins: ["*"],
  },
  devicePreview: {
    currentDevice: "desktop",
    orientation: "portrait",
    showDeviceFrame: true,
    deviceScale: 1,
  },
  testing: {
    enableTesting: false,
    testScenarios: [],
    performanceMonitoring: true,
    errorTracking: true,
    loadTesting: false,
    maxConcurrentUsers: 100,
  },
  isDraft: true,
  isPublished: false,
  tags: [],
  priority: "medium",
};

// Configuration presets
export const widgetPresets = {
  minimal: {
    basic: {
      name: "Minimal Support Widget",
      description: "Clean and simple support widget",
      aiProvider: "openai" as AIProvider,
      aiModel: "gpt-3.5-turbo",
    },
    styling: {
      primaryColor: "#000000",
      secondaryColor: "#ffffff",
      borderRadius: 4,
      shadowIntensity: "sm" as const,
    },
    behavior: {
      showBranding: false,
      enableSoundNotifications: false,
    },
  },
  modern: {
    basic: {
      name: "Modern Support Widget",
      description: "Contemporary design with smooth animations",
      aiProvider: "openai" as AIProvider,
      aiModel: "gpt-4",
    },
    styling: {
      primaryColor: "#6366f1",
      secondaryColor: "#ffffff",
      borderRadius: 16,
      shadowIntensity: "lg" as const,
      animation: "slide" as AnimationType,
    },
    behavior: {
      autoOpen: true,
      autoOpenDelay: 5000,
      showTypingIndicator: true,
    },
  },
  enterprise: {
    basic: {
      name: "Enterprise Support Widget",
      description: "Professional widget for enterprise use",
      aiProvider: "openai" as AIProvider,
      aiModel: "gpt-4",
    },
    styling: {
      primaryColor: "#1f2937",
      secondaryColor: "#f9fafb",
      fontFamily: "Inter" as FontFamily,
      borderRadius: 8,
    },
    behavior: {
      enableFileUpload: true,
      enableMarkdown: true,
      showBranding: true,
    },
    advanced: {
      enableAnalytics: true,
      enableGDPR: true,
      enableEncryption: true,
      rateLimitPerMinute: 10,
    },
  },
} as const;
